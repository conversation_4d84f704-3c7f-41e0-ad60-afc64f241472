#!/usr/bin/env python3
"""Bot management handler for Telegram bot."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.constants import ParseMode

from .base_handler import BaseTelegramHandler
from ....core.credential_utils import list_profiles


class BotManagementHandler(BaseTelegramHandler):
    """Handler for bot management operations."""

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass
    
    async def handle_start_bot(self, update: Update, context) -> None:
        """Handle /start command for starting trading bots"""
        if len(context.args) < 2:
            await self._send_error_message(
                update, 
                "Sử dụng: /start <symbol> <amount> [test]"
            )
            return

        symbol = context.args[0].upper()
        amount = context.args[1]
        
        # Validate inputs
        if not self._validate_symbol(symbol):
            await self._send_error_message(update, f"Symbol không hợp lệ: {symbol}")
            return
        
        if not self._validate_amount(amount):
            await self._send_error_message(update, f"Amount không hợp lệ: {amount}")
            return
        
        try:
            # Build command arguments
            cmd_args = [self.bot_script_path, "start", symbol, "--amount", amount]
            
            # Add additional arguments
            for arg in context.args[2:]:
                if arg.lower() in ["test", "testmode", "test_mode"]:
                    cmd_args.append("--test-mode")
                else:
                    cmd_args.append(arg)
            
            # Start bot using bot.sh
            result = await self._execute_botsh_command(cmd_args)
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"🚀 **Bot đã khởi động!**\n\n"
                    f"Symbol: `{symbol}`\n"
                    f"Amount: `${amount}`\n\n"
                    f"Sử dụng `/status {symbol}` để xem trạng thái.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error starting bot: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stop_bot(self, update: Update, context) -> None:
        """Handle /stop command for stopping trading bots"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /stop <symbol>")
            return

        symbol = context.args[0].upper()
        
        try:
            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Xác nhận", callback_data=f"stop_confirm_{symbol}"),
                    InlineKeyboardButton("❌ Hủy", callback_data="stop_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                f"⚠️ **Xác nhận dừng bot**\n\n"
                f"Bạn có chắc muốn dừng bot `{symbol}`?",
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"Error in handle_stop_bot: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stop_confirm(self, query, symbol: str) -> None:
        """Handle bot stop confirmation"""
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "stop", symbol
            ])
            
            if result[0] == 0:
                await query.edit_message_text(
                    f"✅ **Bot {symbol} đã được dừng**",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Lỗi dừng bot:** {result[2] or result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
                
        except Exception as e:
            self.logger.error(f"Error stopping bot: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_list_bots(self, update: Update, context) -> None:
        """Handle /list command for listing active bots"""
        try:
            result = await self._execute_botsh_command([self.bot_script_path, "list"])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"🤖 **Danh sách bot:**\n\n```\n{result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error listing bots: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_status_bot(self, update: Update, context) -> None:
        """Handle /status command for bot status"""
        if not context.args:
            # Show all bots status
            await self.handle_list_bots(update, context)
            return
        
        symbol = context.args[0].upper()
        
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "status", symbol
            ])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"📊 **Trạng thái bot {symbol}:**\n\n```\n{result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error getting bot status: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_logs_bot(self, update: Update, context) -> None:
        """Handle /logs command for bot logs"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /logs <symbol> [lines]")
            return
        
        symbol = context.args[0].upper()
        lines = context.args[1] if len(context.args) > 1 else "50"
        
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "logs", symbol, "--lines", lines
            ])
            
            if result[0] == 0:
                # Truncate if too long
                output = result[1]
                if len(output) > 4000:
                    output = output[-4000:] + "\n\n... (truncated)"
                
                await update.message.reply_text(
                    f"📋 **Logs bot {symbol} ({lines} dòng cuối):**\n\n```\n{output}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error getting bot logs: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_restart_bot(self, update: Update, context) -> None:
        """Handle /restart command for restarting bots"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /restart <symbol>")
            return

        symbol = context.args[0].upper()
        
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "restart", symbol
            ])
            
            if result[0] == 0:
                await self._send_success_message(update, f"Bot {symbol} đã được khởi động lại")
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error restarting bot: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stopall_bots(self, update: Update, context) -> None:
        """Handle /stopall command for stopping all bots"""
        try:
            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Xác nhận", callback_data="confirm_stopall"),
                    InlineKeyboardButton("❌ Hủy", callback_data="cancel_stopall")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                "⚠️ **Xác nhận dừng tất cả bot**\n\n"
                "Bạn có chắc muốn dừng TẤT CẢ bot đang chạy?",
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"Error in handle_stopall_bots: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stopall_confirm(self, query) -> None:
        """Handle stop all bots confirmation"""
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "stopall"
            ])
            
            if result[0] == 0:
                await query.edit_message_text(
                    "✅ **Tất cả bot đã được dừng**",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Lỗi dừng bot:** {result[2] or result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
                
        except Exception as e:
            self.logger.error(f"Error stopping all bots: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )
