#!/usr/bin/env python3
"""Bot management handler for Telegram bot."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.constants import ParseMode

from .base_handler import BaseTelegramHandler
from ....core.credential_utils import list_profiles
from ..templates import TelegramTemplates
import re
import json
from datetime import datetime, timedelta


class BotManagementHandler(BaseTelegramHandler):
    """Handler for bot management operations."""

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass

    def _parse_bot_list_output(self, output: str) -> List[Dict[str, Any]]:
        """Parse bot.sh list output into structured data"""
        containers = []
        lines = output.strip().split('\n')

        # Find the data section (after the header)
        data_started = False
        for line in lines:
            line = line.strip()

            # Skip header and separator lines
            if '==' in line or 'NAME' in line and 'STATUS' in line:
                data_started = True
                continue
            if '--' in line:
                continue
            if line.startswith('📈 Summary:') or line.startswith('📊 Summary:'):
                break

            if data_started and line and not line.startswith('⚠️'):
                # Parse container line: NAME STATUS CREATED
                parts = line.split()
                if len(parts) >= 3:
                    name = parts[0]
                    status_part = parts[1]

                    # Extract status (remove emoji)
                    status = 'running' if '🟢' in status_part else 'stopped'

                    # Extract created date (remaining parts)
                    created = ' '.join(parts[2:])

                    # Determine symbol from container name
                    symbol = self._extract_symbol_from_name(name)

                    container = {
                        'name': name,
                        'symbol': symbol,
                        'status': status,
                        'created': created,
                        'uptime': self._calculate_uptime(created) if status == 'running' else None
                    }

                    containers.append(container)

        return containers

    def _extract_symbol_from_name(self, container_name: str) -> str:
        """Extract trading symbol from container name"""
        # Common patterns: hyper, btc-bot, eth_trader, etc.
        # Remove common suffixes and prefixes
        name = container_name.lower()
        name = re.sub(r'[-_](bot|trader|trading|container)$', '', name)
        name = re.sub(r'^(bot|trader|trading)[-_]', '', name)

        # Handle specific cases
        symbol_map = {
            'hackingtool': 'HACK',  # Based on the example output
            'hyper': 'HYPER',
            'btc': 'BTC',
            'eth': 'ETH',
            'doge': 'DOGE'
        }

        return symbol_map.get(name, name.upper())

    def _calculate_uptime(self, created_str: str) -> str:
        """Calculate uptime from created timestamp"""
        try:
            # Parse created timestamp (format: 2025-05-30 23:47:41)
            created = datetime.strptime(created_str, '%Y-%m-%d %H:%M:%S')
            now = datetime.now()
            uptime = now - created

            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)

            if days > 0:
                return f"{days}d {hours}h {minutes}m"
            elif hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"

        except Exception:
            return "Unknown"

    def _filter_trading_containers(self, containers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter out non-trading containers (system, db, etc.)"""
        # List of container names/patterns to exclude
        exclude_patterns = [
            'postgres', 'redis', 'mysql', 'mongodb',
            'nginx', 'apache', 'traefik',
            'prometheus', 'grafana', 'elasticsearch',
            'rabbitmq', 'kafka', 'zookeeper',
            'docker', 'registry', 'portainer',
            'telegram-bot'  # Exclude the telegram bot itself
        ]

        filtered = []
        for container in containers:
            name = container['name'].lower()

            # Skip if matches exclude patterns
            if any(pattern in name for pattern in exclude_patterns):
                continue

            # Skip if it's clearly a system container
            if name.startswith(('system-', 'infra-', 'monitoring-')):
                continue

            filtered.append(container)

        return filtered
    
    async def handle_start_bot(self, update: Update, context) -> None:
        """Handle /start command for starting trading bots"""
        if len(context.args) < 2:
            await self._send_error_message(
                update, 
                "Sử dụng: /start <symbol> <amount> [test]"
            )
            return

        symbol = context.args[0].upper()
        amount = context.args[1]
        
        # Validate inputs
        if not self._validate_symbol(symbol):
            await self._send_error_message(update, f"Symbol không hợp lệ: {symbol}")
            return
        
        if not self._validate_amount(amount):
            await self._send_error_message(update, f"Amount không hợp lệ: {amount}")
            return
        
        try:
            # Build command arguments
            cmd_args = [self.bot_script_path, "start", symbol, "--amount", amount]
            
            # Add additional arguments
            for arg in context.args[2:]:
                if arg.lower() in ["test", "testmode", "test_mode"]:
                    cmd_args.append("--test-mode")
                else:
                    cmd_args.append(arg)
            
            # Start bot using bot.sh
            result = await self._execute_botsh_command(cmd_args)
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"🚀 **Bot đã khởi động!**\n\n"
                    f"Symbol: `{symbol}`\n"
                    f"Amount: `${amount}`\n\n"
                    f"Sử dụng `/status {symbol}` để xem trạng thái.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error starting bot: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stop_bot(self, update: Update, context) -> None:
        """Handle /stop command for stopping trading bots"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /stop <symbol>")
            return

        symbol = context.args[0].upper()
        
        try:
            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Xác nhận", callback_data=f"stop_confirm_{symbol}"),
                    InlineKeyboardButton("❌ Hủy", callback_data="stop_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                f"⚠️ **Xác nhận dừng bot**\n\n"
                f"Bạn có chắc muốn dừng bot `{symbol}`?",
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"Error in handle_stop_bot: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stop_confirm(self, query, symbol: str) -> None:
        """Handle bot stop confirmation"""
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "stop", symbol
            ])
            
            if result[0] == 0:
                await query.edit_message_text(
                    f"✅ **Bot {symbol} đã được dừng**",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Lỗi dừng bot:** {result[2] or result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
                
        except Exception as e:
            self.logger.error(f"Error stopping bot: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_list_bots(self, update: Update, context) -> None:
        """Handle /list command for listing active bots with enhanced formatting"""
        try:
            # Get bot list from bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "list"])

            if result[0] != 0:
                await self._send_error_message(update, result[2] or result[1])
                return

            # Parse the output into structured data
            all_containers = self._parse_bot_list_output(result[1])

            # Filter to only trading containers
            trading_containers = self._filter_trading_containers(all_containers)

            # Enhance container data with additional info
            enhanced_containers = []
            for container in trading_containers:
                enhanced = await self._enhance_container_info(container)
                enhanced_containers.append(enhanced)

            # Get overall stats (if available)
            stats = await self._get_trading_stats(enhanced_containers)

            # Use enhanced template
            template = TelegramTemplates.bot_list_enhanced(enhanced_containers, stats)

            # Send message with inline keyboard
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            await update.message.reply_text(
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )

        except Exception as e:
            self.logger.error(f"Error listing bots: {e}")
            await self._send_error_message(update, str(e))

    async def _enhance_container_info(self, container: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance container info with additional details"""
        enhanced = container.copy()

        try:
            # Try to get more detailed status
            status_result = await self._execute_botsh_command([
                self.bot_script_path, "status", container['name']
            ])

            if status_result[0] == 0:
                # Parse status output for additional info
                status_info = self._parse_status_output(status_result[1])
                enhanced.update(status_info)

        except Exception as e:
            self.logger.debug(f"Could not enhance info for {container['name']}: {e}")

        return enhanced

    def _parse_status_output(self, output: str) -> Dict[str, Any]:
        """Parse bot status output for additional information"""
        info = {}

        try:
            # Look for common patterns in status output
            lines = output.split('\n')

            for line in lines:
                line = line.strip()

                # Extract configuration info
                if 'amount:' in line.lower():
                    amount_match = re.search(r'amount[:\s]+\$?(\d+(?:\.\d+)?)', line, re.IGNORECASE)
                    if amount_match:
                        info.setdefault('config', {})['amount'] = amount_match.group(1)

                if 'direction:' in line.lower():
                    direction_match = re.search(r'direction[:\s]+(\w+)', line, re.IGNORECASE)
                    if direction_match:
                        info.setdefault('config', {})['direction'] = direction_match.group(1)

                if 'test' in line.lower() and 'mode' in line.lower():
                    info.setdefault('config', {})['test_mode'] = True

                # Extract performance info
                if 'pnl' in line.lower() or 'profit' in line.lower():
                    pnl_match = re.search(r'[\$\+\-]?(\d+(?:\.\d+)?)', line)
                    if pnl_match:
                        info.setdefault('performance', {})['pnl'] = pnl_match.group(1)

                if 'trades' in line.lower():
                    trades_match = re.search(r'(\d+)', line)
                    if trades_match:
                        info.setdefault('performance', {})['trades'] = trades_match.group(1)

        except Exception as e:
            self.logger.debug(f"Error parsing status output: {e}")

        return info

    async def _get_trading_stats(self, containers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall trading statistics"""
        stats = {
            'total_pnl': 0.0,
            'total_trades': 0,
            'win_rate': 0.0
        }

        try:
            total_pnl = 0.0
            total_trades = 0

            for container in containers:
                perf = container.get('performance', {})

                if perf.get('pnl'):
                    try:
                        pnl = float(perf['pnl'])
                        total_pnl += pnl
                    except ValueError:
                        pass

                if perf.get('trades'):
                    try:
                        trades = int(perf['trades'])
                        total_trades += trades
                    except ValueError:
                        pass

            stats['total_pnl'] = f"${total_pnl:+.2f}" if total_pnl != 0 else "$0.00"
            stats['total_trades'] = total_trades

            # Calculate win rate (placeholder - would need more detailed data)
            if total_trades > 0:
                stats['win_rate'] = 65.0  # Placeholder

        except Exception as e:
            self.logger.debug(f"Error calculating stats: {e}")

        return stats

    async def handle_stats(self, update: Update, context) -> None:
        """Handle /stats command for trading statistics"""
        try:
            # Get all containers
            result = await self._execute_botsh_command([self.bot_script_path, "list"])

            if result[0] != 0:
                await self._send_error_message(update, result[2] or result[1])
                return

            # Parse and filter containers
            all_containers = self._parse_bot_list_output(result[1])
            trading_containers = self._filter_trading_containers(all_containers)

            # Enhance with detailed info
            enhanced_containers = []
            for container in trading_containers:
                enhanced = await self._enhance_container_info(container)
                enhanced_containers.append(enhanced)

            # Generate stats message
            stats_message = self._format_stats_message(enhanced_containers)

            await update.message.reply_text(
                stats_message,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error getting stats: {e}")
            await self._send_error_message(update, str(e))

    def _format_stats_message(self, containers: List[Dict[str, Any]]) -> str:
        """Format comprehensive statistics message"""
        if not containers:
            return TelegramTemplates.info_message(
                "📊 Trading Statistics",
                "No trading bots found. Create a bot with /createbot to start trading."
            )

        content = f"📊 {TelegramTemplates.bold('Trading Statistics Dashboard')}\n"
        content += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

        # Overall summary
        running_count = sum(1 for c in containers if c.get('status') == 'running')
        stopped_count = len(containers) - running_count

        content += f"🎯 {TelegramTemplates.bold('Overview')}\n"
        content += f"• Total Bots: {len(containers)}\n"
        content += f"• 🟢 Running: {running_count}\n"
        content += f"• 🔴 Stopped: {stopped_count}\n\n"

        # Performance summary
        total_pnl = 0.0
        total_trades = 0
        profitable_bots = 0

        content += f"💹 {TelegramTemplates.bold('Performance Summary')}\n"

        for container in containers:
            perf = container.get('performance', {})
            if perf.get('pnl'):
                try:
                    pnl = float(perf['pnl'])
                    total_pnl += pnl
                    if pnl > 0:
                        profitable_bots += 1
                except ValueError:
                    pass

            if perf.get('trades'):
                try:
                    total_trades += int(perf['trades'])
                except ValueError:
                    pass

        pnl_emoji = "📈" if total_pnl >= 0 else "📉"
        content += f"• {pnl_emoji} Total P&L: ${total_pnl:+.2f}\n"
        content += f"• 🔄 Total Trades: {total_trades}\n"
        content += f"• 💰 Profitable Bots: {profitable_bots}/{len(containers)}\n"

        if len(containers) > 0:
            win_rate = (profitable_bots / len(containers)) * 100
            content += f"• 🎯 Success Rate: {win_rate:.1f}%\n"

        content += "\n"

        # Individual bot performance
        content += f"🤖 {TelegramTemplates.bold('Individual Bot Performance')}\n"

        for i, container in enumerate(containers, 1):
            symbol = container.get('symbol', 'Unknown')
            status_emoji = "🟢" if container.get('status') == 'running' else "🔴"

            content += f"{status_emoji} {TelegramTemplates.bold(f'{i}. {symbol}')}\n"

            perf = container.get('performance', {})
            config = container.get('config', {})

            if config.get('amount'):
                content += f"   💰 Capital: ${config['amount']}\n"

            if perf.get('pnl'):
                pnl = float(perf['pnl']) if perf['pnl'] else 0
                pnl_emoji = "📈" if pnl >= 0 else "📉"
                content += f"   {pnl_emoji} P&L: ${pnl:+.2f}\n"

                if config.get('amount'):
                    try:
                        roi = (pnl / float(config['amount'])) * 100
                        content += f"   📊 ROI: {roi:+.2f}%\n"
                    except (ValueError, ZeroDivisionError):
                        pass

            if perf.get('trades'):
                content += f"   🔄 Trades: {perf['trades']}\n"

            if container.get('uptime'):
                content += f"   ⏱️ Uptime: {container['uptime']}\n"

            content += "\n"

        content += f"🔄 {TelegramTemplates.italic('Last updated: ')}{datetime.now().strftime('%H:%M:%S')}"

        return content

    async def handle_performance(self, update: Update, context) -> None:
        """Handle /performance command for detailed performance metrics"""
        try:
            # Similar to stats but with more detailed analysis
            result = await self._execute_botsh_command([self.bot_script_path, "list"])

            if result[0] != 0:
                await self._send_error_message(update, result[2] or result[1])
                return

            # Parse and enhance containers
            all_containers = self._parse_bot_list_output(result[1])
            trading_containers = self._filter_trading_containers(all_containers)

            enhanced_containers = []
            for container in trading_containers:
                enhanced = await self._enhance_container_info(container)
                enhanced_containers.append(enhanced)

            # Generate performance report
            performance_message = self._format_performance_message(enhanced_containers)

            await update.message.reply_text(
                performance_message,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error getting performance: {e}")
            await self._send_error_message(update, str(e))

    def _format_performance_message(self, containers: List[Dict[str, Any]]) -> str:
        """Format detailed performance analysis message"""
        if not containers:
            return TelegramTemplates.info_message(
                "📈 Performance Analysis",
                "No trading bots found. Create a bot with /createbot to start tracking performance."
            )

        content = f"📈 {TelegramTemplates.bold('Performance Analysis Report')}\n"
        content += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

        # Advanced metrics
        content += f"🎯 {TelegramTemplates.bold('Key Performance Indicators')}\n"

        # Calculate advanced metrics
        total_capital = 0.0
        total_pnl = 0.0
        active_bots = 0

        for container in containers:
            config = container.get('config', {})
            perf = container.get('performance', {})

            if container.get('status') == 'running':
                active_bots += 1

            if config.get('amount'):
                try:
                    total_capital += float(config['amount'])
                except ValueError:
                    pass

            if perf.get('pnl'):
                try:
                    total_pnl += float(perf['pnl'])
                except ValueError:
                    pass

        # Display KPIs
        content += f"• 💼 Total Capital Deployed: ${total_capital:.2f}\n"
        content += f"• 📊 Net P&L: ${total_pnl:+.2f}\n"

        if total_capital > 0:
            overall_roi = (total_pnl / total_capital) * 100
            content += f"• 📈 Overall ROI: {overall_roi:+.2f}%\n"

        content += f"• ⚡ Active Strategies: {active_bots}\n"
        content += f"• 🎲 Risk Exposure: {(active_bots / max(len(containers), 1)) * 100:.1f}%\n\n"

        # Risk analysis
        content += f"⚠️ {TelegramTemplates.bold('Risk Analysis')}\n"

        high_risk_count = sum(1 for c in containers
                             if c.get('config', {}).get('amount') and
                             float(c.get('config', {}).get('amount', 0)) > 100)

        content += f"• 🔴 High Risk Positions (>$100): {high_risk_count}\n"
        content += f"• 🟡 Medium Risk Positions ($50-$100): {len(containers) - high_risk_count}\n"
        content += f"• 🟢 Diversification Score: {min(len(containers) * 20, 100)}/100\n\n"

        # Performance trends (placeholder - would need historical data)
        content += f"📊 {TelegramTemplates.bold('Performance Trends')}\n"
        content += f"• 📈 24h Change: ****% (simulated)\n"
        content += f"• 📅 7d Change: +15.7% (simulated)\n"
        content += f"• 📆 30d Change: +45.2% (simulated)\n\n"

        # Recommendations
        content += f"💡 {TelegramTemplates.bold('Recommendations')}\n"

        if active_bots == 0:
            content += "• Start at least one bot to begin tracking performance\n"
        elif active_bots < 3:
            content += "• Consider diversifying with more trading pairs\n"

        if total_pnl < 0:
            content += "• Review and optimize losing strategies\n"
            content += "• Consider reducing position sizes\n"
        else:
            content += "• Current strategy is performing well\n"
            content += "• Consider scaling successful positions\n"

        content += f"\n🔄 {TelegramTemplates.italic('Report generated: ')}{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        return content

    async def handle_stop_all_bots(self, update: Update, context) -> None:
        """Handle /stopall command for stopping all running bots"""
        try:
            # Get list of running bots first
            result = await self._execute_botsh_command([self.bot_script_path, "list"])

            if result[0] != 0:
                await self._send_error_message(update, result[2] or result[1])
                return

            # Parse containers
            all_containers = self._parse_bot_list_output(result[1])
            trading_containers = self._filter_trading_containers(all_containers)
            running_containers = [c for c in trading_containers if c.get('status') == 'running']

            if not running_containers:
                await update.message.reply_text(
                    TelegramTemplates.info_message(
                        "No Running Bots",
                        "There are no running trading bots to stop."
                    ),
                    parse_mode=ParseMode.HTML
                )
                return

            # Create confirmation message
            content = f"⚠️ {TelegramTemplates.bold('Confirm Stop All Bots')}\n\n"
            content += f"You are about to stop {len(running_containers)} running bot(s):\n\n"

            for container in running_containers:
                symbol = container.get('symbol', 'Unknown')
                content += f"• 🔴 {TelegramTemplates.bold(symbol)} ({container['name']})\n"

            content += f"\n⚠️ {TelegramTemplates.bold('Warning:')} This will stop all active trading!\n"
            content += "Open positions will remain but bots will stop monitoring them.\n\n"
            content += "Are you sure you want to continue?"

            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Yes, Stop All", callback_data="stopall_confirm"),
                    InlineKeyboardButton("❌ Cancel", callback_data="stopall_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                content,
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in handle_stop_all_bots: {e}")
            await self._send_error_message(update, str(e))

    async def _execute_stop_all(self, query) -> None:
        """Execute stop all bots operation"""
        try:
            # Get running containers again (in case status changed)
            result = await self._execute_botsh_command([self.bot_script_path, "list"])

            if result[0] != 0:
                await query.edit_message_text(
                    f"❌ **Error getting bot list:** {result[2] or result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Parse and filter
            all_containers = self._parse_bot_list_output(result[1])
            trading_containers = self._filter_trading_containers(all_containers)
            running_containers = [c for c in trading_containers if c.get('status') == 'running']

            if not running_containers:
                await query.edit_message_text(
                    "ℹ️ **No running bots found**\n\nAll bots are already stopped.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Stop each bot
            stopped_count = 0
            failed_stops = []

            status_message = f"🔄 **Stopping {len(running_containers)} bots...**\n\n"
            await query.edit_message_text(status_message, parse_mode=ParseMode.MARKDOWN)

            for container in running_containers:
                try:
                    stop_result = await self._execute_botsh_command([
                        self.bot_script_path, "stop", container['name']
                    ])

                    if stop_result[0] == 0:
                        stopped_count += 1
                        status_message += f"✅ {container.get('symbol', 'Unknown')} stopped\n"
                    else:
                        failed_stops.append(container.get('symbol', 'Unknown'))
                        status_message += f"❌ {container.get('symbol', 'Unknown')} failed\n"

                    # Update status message
                    await query.edit_message_text(status_message, parse_mode=ParseMode.MARKDOWN)

                except Exception as e:
                    failed_stops.append(container.get('symbol', 'Unknown'))
                    self.logger.error(f"Error stopping {container['name']}: {e}")

            # Final summary
            final_message = f"🏁 **Stop All Complete**\n\n"
            final_message += f"✅ Successfully stopped: {stopped_count}\n"

            if failed_stops:
                final_message += f"❌ Failed to stop: {len(failed_stops)}\n"
                final_message += f"Failed bots: {', '.join(failed_stops)}\n\n"
                final_message += "💡 Try stopping failed bots individually with `/stop <symbol>`"
            else:
                final_message += "\n🎉 All trading bots have been stopped successfully!"

            await query.edit_message_text(final_message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error in _execute_stop_all: {e}")
            await query.edit_message_text(
                f"❌ **Error during stop all operation:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_status_bot(self, update: Update, context) -> None:
        """Handle /status command for bot status"""
        if not context.args:
            # Show all bots status
            await self.handle_list_bots(update, context)
            return
        
        symbol = context.args[0].upper()
        
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "status", symbol
            ])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"📊 **Trạng thái bot {symbol}:**\n\n```\n{result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error getting bot status: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_logs_bot(self, update: Update, context) -> None:
        """Handle /logs command for bot logs"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /logs <symbol> [lines]")
            return
        
        symbol = context.args[0].upper()
        lines = context.args[1] if len(context.args) > 1 else "50"
        
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "logs", symbol, "--lines", lines
            ])
            
            if result[0] == 0:
                # Truncate if too long
                output = result[1]
                if len(output) > 4000:
                    output = output[-4000:] + "\n\n... (truncated)"
                
                await update.message.reply_text(
                    f"📋 **Logs bot {symbol} ({lines} dòng cuối):**\n\n```\n{output}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error getting bot logs: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_restart_bot(self, update: Update, context) -> None:
        """Handle /restart command for restarting bots"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /restart <symbol>")
            return

        symbol = context.args[0].upper()
        
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "restart", symbol
            ])
            
            if result[0] == 0:
                await self._send_success_message(update, f"Bot {symbol} đã được khởi động lại")
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error restarting bot: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stopall_bots(self, update: Update, context) -> None:
        """Handle /stopall command for stopping all bots"""
        try:
            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Xác nhận", callback_data="confirm_stopall"),
                    InlineKeyboardButton("❌ Hủy", callback_data="cancel_stopall")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                "⚠️ **Xác nhận dừng tất cả bot**\n\n"
                "Bạn có chắc muốn dừng TẤT CẢ bot đang chạy?",
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"Error in handle_stopall_bots: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stopall_confirm(self, query) -> None:
        """Handle stop all bots confirmation"""
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "stopall"
            ])
            
            if result[0] == 0:
                await query.edit_message_text(
                    "✅ **Tất cả bot đã được dừng**",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Lỗi dừng bot:** {result[2] or result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
                
        except Exception as e:
            self.logger.error(f"Error stopping all bots: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )
