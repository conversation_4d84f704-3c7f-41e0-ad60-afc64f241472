"""Bot Wizards Handler - Handle bot creation and setup wizards"""
import os
import json
from typing import Dict, List, Optional, Any
from ..telegram_base import TelegramBaseHandler


class BotWizardsHandler(TelegramBaseHandler):
    """Handles bot creation wizards and multi-step setup processes"""
    
    def __init__(self, session_manager):
        super().__init__('BotWizardsHandler')
        self.session_manager = session_manager
        
        # Wizard step definitions
        self.wizard_steps = {
            'bot_creation': [
                'select_credentials',
                'select_config', 
                'enter_bot_name',
                'enter_symbol',
                'enter_amount',
                'select_direction',
                'confirm_creation'
            ]
        }
    
    def get_current_time(self) -> str:
        """Get current time as ISO string"""
        from datetime import datetime
        return datetime.now().isoformat()

    async def execute_command(self, command: List[str]) -> str:
        """Execute shell command and return output"""
        import subprocess
        import asyncio

        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/app"
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                return stdout.decode('utf-8', errors='ignore').strip()
            else:
                return stderr.decode('utf-8', errors='ignore').strip()

        except Exception as e:
            self.logger.error(f"Error executing command {' '.join(command)}: {e}")
            return f"Error: {str(e)}"
    
    # ===============================
    # Bot Creation Wizard
    # ===============================
    
    async def handle_createbot_command(self, update, context):
        """Handle /createbot command - Start bot creation wizard"""
        try:
            chat_id = update.effective_chat.id
            
            # Check prerequisites using bot.sh command
            command = ["./bot.sh", "list-credentials"]
            result = await self.execute_command(command)

            if "No credential profiles found" in result:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "❌ <b>Missing Prerequisites</b>\n\n"
                    "You need to set up credentials first.\n\n"
                    "💡 Use `/addcreds` to add API credentials."
                )
                return
            
            if not os.path.exists(configs_path) or not os.listdir(configs_path):
                await self.send_message(
                    context.bot,
                    chat_id,
                    "❌ <b>Missing Prerequisites</b>\n\n"
                    "You need to create a configuration first.\n\n"
                    "💡 Use `/createconfig` to create a trading configuration."
                )
                return
            
            # Start wizard
            wizard_data = {
                'type': 'bot_creation',
                'step': 0,
                'data': {},
                'started_at': self.get_current_time()
            }
            
            self.session_manager.set_wizard_session(chat_id, wizard_data)
            
            await self._show_credentials_selection(update, context)
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "createbot")
            )
    
    async def _show_credentials_selection(self, update, context):
        """Step 1: Select API credentials"""
        try:
            chat_id = update.effective_chat.id
            
            # Get available credentials
            credentials = await self._get_available_credentials()
            
            if not credentials:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "❌ No credentials available.\n\nUse `/addcreds` to add credentials first."
                )
                return
            
            # Create selection keyboard
            keyboard_buttons = []
            for cred in credentials:
                display_name = cred.get('display_name', cred['filename'])
                exchange = cred.get('exchange', 'Unknown')
                keyboard_buttons.append([(f"🔑 {display_name} ({exchange})", f"wiz_cred_{cred['filename']}")])
            
            keyboard_buttons.append([("❌ Cancel", "wizard_cancel")])
            keyboard = self.create_keyboard(keyboard_buttons)
            
            message = "🤖 <b>Bot Creation Wizard - Step 1/7</b>\n\n"
            message += "🔑 <b>Select API Credentials:</b>\n\n"
            for i, cred in enumerate(credentials, 1):
                display_name = cred.get('display_name', cred['filename'])
                exchange = cred.get('exchange', 'Unknown')
                message += f"{i}. <b>{display_name}</b>\n"
                message += f"   Exchange: {exchange}\n"
                message += f"   Created: {cred.get('created_at', 'Unknown')[:10]}\n\n"
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "credentials_selection")
            )
    
    async def _show_config_selection(self, update, context):
        """Step 2: Select trading configuration"""
        try:
            chat_id = update.effective_chat.id
            
            # Get available configs
            configs = await self._get_available_configs()
            
            if not configs:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "❌ No configurations available.\n\nUse `/createconfig` to create a configuration first."
                )
                return
            
            # Create selection keyboard
            keyboard_buttons = []
            for config in configs:
                strategy = config.get('strategy', 'Unknown')
                keyboard_buttons.append([(f"⚙️ {config['name']} ({strategy})", f"wiz_config_{config['filename']}")])
            
            keyboard_buttons.append([("🔙 Back", "wiz_back"), ("❌ Cancel", "wizard_cancel")])
            keyboard = self.create_keyboard(keyboard_buttons)
            
            message = "🤖 <b>Bot Creation Wizard - Step 2/7</b>\n\n"
            message += "⚙️ <b>Select Trading Configuration:</b>\n\n"
            
            for i, config in enumerate(configs, 1):
                strategy = config.get('strategy', 'Unknown')
                symbol = config.get('symbol', 'N/A')
                message += f"{i}. <b>{config['name']}</b>\n"
                message += f"   Strategy: {strategy}\n"
                message += f"   Symbol: {symbol}\n"
                message += f"   Created: {config.get('created_at', 'Unknown')[:10]}\n\n"
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "config_selection")
            )
    
    async def _show_bot_name_input(self, update, context):
        """Step 3: Enter bot name"""
        try:
            chat_id = update.effective_chat.id
            
            # Get session data for suggestions
            session = self.session_manager.get_wizard_session(chat_id)
            selected_config = session['data'].get('config_name', 'bot')
            
            # Generate suggestions
            suggestions = [
                f"{selected_config}_trader",
                f"{selected_config}_bot_1",
                f"auto_{selected_config}",
                f"{selected_config}_{self.get_current_time()[:10].replace('-', '')}"
            ]
            
            keyboard_buttons = []
            for suggestion in suggestions:
                keyboard_buttons.append([(f"💡 {suggestion}", f"wiz_name_{suggestion}")])
            
            keyboard_buttons.append([("🔙 Back", "wiz_back"), ("❌ Cancel", "wizard_cancel")])
            keyboard = self.create_keyboard(keyboard_buttons)
            
            message = "🤖 <b>Bot Creation Wizard - Step 3/7</b>\n\n"
            message += "📝 <b>Enter Bot Name:</b>\n\n"
            message += "Choose a unique name for your trading bot.\n\n"
            message += "<b>Suggestions:</b>\n"
            for suggestion in suggestions:
                message += f"• `{suggestion}`\n"
            message += "\n💡 Or type a custom name directly."
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
            # Update session to expect text input
            session['expecting_input'] = 'bot_name'
            self.session_manager.set_wizard_session(chat_id, session)
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "bot_name_input")
            )
    
    async def _show_symbol_input(self, update, context):
        """Step 4: Enter trading symbol"""
        try:
            chat_id = update.effective_chat.id
            
            # Popular symbols suggestions
            popular_symbols = [
                "HYPER/USDT:USDT",
                "BTC/USDT:USDT", 
                "ETH/USDT:USDT",
                "BNB/USDT:USDT",
                "SOL/USDT:USDT",
                "XRP/USDT:USDT"
            ]
            
            keyboard_buttons = []
            for symbol in popular_symbols:
                keyboard_buttons.append([(f"💰 {symbol}", f"wiz_symbol_{symbol}")])
            
            keyboard_buttons.append([("🔙 Back", "wiz_back"), ("❌ Cancel", "wizard_cancel")])
            keyboard = self.create_keyboard(keyboard_buttons)
            
            message = "🤖 <b>Bot Creation Wizard - Step 4/7</b>\n\n"
            message += "💰 <b>Select Trading Symbol:</b>\n\n"
            message += "Choose the cryptocurrency pair to trade.\n\n"
            message += "<b>Popular symbols:</b>\n"
            for symbol in popular_symbols:
                message += f"• `{symbol}`\n"
            message += "\n💡 Or type a custom symbol (e.g., `DOGE/USDT:USDT`)"
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
            # Update session to expect text input
            session = self.session_manager.get_wizard_session(chat_id)
            session['expecting_input'] = 'symbol'
            self.session_manager.set_wizard_session(chat_id, session)
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "symbol_input")
            )
    
    async def _show_amount_input(self, update, context):
        """Step 5: Enter trading amount"""
        try:
            chat_id = update.effective_chat.id
            
            # Amount suggestions
            amounts = ["10", "25", "50", "100", "250", "500"]
            
            keyboard_buttons = []
            for amount in amounts:
                keyboard_buttons.append([(f"💵 ${amount}", f"wiz_amount_{amount}")])
            
            keyboard_buttons.append([("🔙 Back", "wiz_back"), ("❌ Cancel", "wizard_cancel")])
            keyboard = self.create_keyboard(keyboard_buttons)
            
            message = "🤖 <b>Bot Creation Wizard - Step 5/7</b>\n\n"
            message += "💵 <b>Enter Trading Amount (USDT):</b>\n\n"
            message += "How much USDT should the bot use per trade?\n\n"
            message += "<b>Quick amounts:</b>\n"
            for amount in amounts:
                message += f"• `${amount} USDT`\n"
            message += "\n💡 Or type a custom amount (numbers only)"
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
            # Update session to expect text input
            session = self.session_manager.get_wizard_session(chat_id)
            session['expecting_input'] = 'amount'
            self.session_manager.set_wizard_session(chat_id, session)
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "amount_input")
            )
    
    async def _show_direction_selection(self, update, context):
        """Step 6: Select trading direction"""
        try:
            chat_id = update.effective_chat.id
            
            keyboard = self.create_keyboard([
                [("📈 Long Only", "wiz_direction_long"), ("📉 Short Only", "wiz_direction_short")],
                [("🔄 Both Directions", "wiz_direction_both")],
                [("🔙 Back", "wiz_back"), ("❌ Cancel", "wizard_cancel")]
            ])
            
            message = "🤖 <b>Bot Creation Wizard - Step 6/7</b>\n\n"
            message += "📊 <b>Select Trading Direction:</b>\n\n"
            message += "<b>Options:</b>\n"
            message += "📈 <b>Long Only</b> - Only buy when price goes up\n"
            message += "📉 <b>Short Only</b> - Only sell when price goes down\n" 
            message += "🔄 <b>Both Directions</b> - Trade in both directions\n\n"
            message += "💡 Most strategies work best with both directions enabled."
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "direction_selection")
            )
    
    async def _show_creation_confirmation(self, update, context):
        """Step 7: Confirm bot creation"""
        try:
            chat_id = update.effective_chat.id
            session = self.session_manager.get_wizard_session(chat_id)
            data = session['data']
            
            # Format confirmation message
            message = "🤖 <b>Bot Creation Wizard - Step 7/7</b>\n\n"
            message += "✅ <b>Confirm Bot Creation:</b>\n\n"
            message += f"<b>Bot Name:</b> `{data.get('bot_name', 'N/A')}`\n"
            message += f"<b>Credentials:</b> `{data.get('credentials_name', 'N/A')}`\n"
            message += f"<b>Config:</b> `{data.get('config_name', 'N/A')}`\n"
            message += f"<b>Symbol:</b> `{data.get('symbol', 'N/A')}`\n"
            message += f"<b>Amount:</b> `${data.get('amount', 'N/A')} USDT`\n"
            message += f"<b>Direction:</b> `{data.get('direction', 'N/A')}`\n\n"
            message += "🚀 <b>Ready to create your trading bot!</b>\n\n"
            message += "⚠️ This will start a new Docker container."
            
            keyboard = self.create_keyboard([
                [("🚀 Create Bot", "wiz_create_confirm"), ("📝 Edit Details", "wiz_edit")],
                [("🔙 Back", "wiz_back"), ("❌ Cancel", "wizard_cancel")]
            ])
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "creation_confirmation")
            )
    
    # ===============================
    # Wizard Navigation
    # ===============================
    
    async def handle_wizard_input(self, update, context):
        """Handle text input during wizard"""
        try:
            chat_id = update.effective_chat.id
            session = self.session_manager.get_wizard_session(chat_id)
            
            if not session or not session.get('expecting_input'):
                return False  # Not in a wizard
            
            user_input = update.message.text.strip()
            input_type = session['expecting_input']
            
            if input_type == 'bot_name':
                # Validate bot name
                if len(user_input) < 3:
                    await self.send_message(
                        context.bot,
                        chat_id,
                        "❌ Bot name must be at least 3 characters long."
                    )
                    return True
                
                if not user_input.replace('_', '').replace('-', '').isalnum():
                    await self.send_message(
                        context.bot,
                        chat_id,
                        "❌ Bot name can only contain letters, numbers, hyphens, and underscores."
                    )
                    return True
                
                # Check if name already exists
                if await self._bot_name_exists(user_input):
                    await self.send_message(
                        context.bot,
                        chat_id,
                        f"❌ Bot name `{user_input}` already exists. Please choose a different name."
                    )
                    return True
                
                # Save and continue
                session['data']['bot_name'] = user_input
                session['step'] = 3
                session['expecting_input'] = None
                self.session_manager.set_wizard_session(chat_id, session)
                
                await self._show_symbol_input(update, context)
                return True
            
            elif input_type == 'symbol':
                # Validate symbol format
                if '/' not in user_input or len(user_input) < 6:
                    await self.send_message(
                        context.bot,
                        chat_id,
                        "❌ Please use format like `BTC/USDT:USDT` or `ETH/USDT`"
                    )
                    return True
                
                # Normalize symbol
                symbol = user_input.upper()
                if ':' not in symbol and 'USDT' in symbol:
                    symbol += ':USDT'
                
                session['data']['symbol'] = symbol
                session['step'] = 4
                session['expecting_input'] = None
                self.session_manager.set_wizard_session(chat_id, session)
                
                await self._show_amount_input(update, context)
                return True
            
            elif input_type == 'amount':
                # Validate amount
                try:
                    amount = float(user_input)
                    if amount <= 0:
                        raise ValueError("Amount must be positive")
                    if amount > 10000:
                        raise ValueError("Amount too large (max 10000)")
                    
                    session['data']['amount'] = str(amount)
                    session['step'] = 5
                    session['expecting_input'] = None
                    self.session_manager.set_wizard_session(chat_id, session)
                    
                    await self._show_direction_selection(update, context)
                    return True
                    
                except ValueError:
                    await self.send_message(
                        context.bot,
                        chat_id,
                        "❌ Please enter a valid number between 1 and 10000."
                    )
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error handling wizard input: {e}")
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "wizard_input")
            )
            return True
    
    # ===============================
    # Command Router
    # ===============================
    
    async def handle_command(self, update, context) -> None:
        """Handle command - Router for wizard commands"""
        command = update.message.text.split()[0].lower()
        
        if command == '/createbot':
            await self.handle_createbot_command(update, context)
        else:
            # Fallback
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"❓ Command `{command}` not handled by BotWizardsHandler"
            )
    
    # ===============================
    # Helper Functions
    # ===============================
    
    async def _get_available_credentials(self) -> List[Dict]:
        """Get list of available credentials using bot.sh command"""
        try:
            command = ["./bot.sh", "list-credentials"]
            result = await self.execute_command(command)

            credentials = []

            # Parse the output to extract credential profiles
            lines = result.split('\n')
            for line in lines:
                line = line.strip()
                # Look for lines with credential icons (🔑 or ⚠️)
                if line.startswith('🔑 ') or line.startswith('⚠️ '):
                    profile_name = line.split(' ', 1)[1] if ' ' in line else ''
                    if profile_name:
                        credentials.append({
                            'profile': profile_name,
                            'display_name': profile_name,
                            'filename': profile_name
                        })

            return credentials

        except Exception as e:
            self.logger.error(f"Error getting credentials: {e}")
            return []
    
    async def _get_available_configs(self) -> List[Dict]:
        """Get list of available configurations"""
        try:
            configs_path = "configs"
            configurations = []
            
            if os.path.exists(configs_path):
                for filename in os.listdir(configs_path):
                    if filename.endswith('.json'):
                        filepath = os.path.join(configs_path, filename)
                        try:
                            with open(filepath, 'r') as f:
                                config_data = json.load(f)
                                config_data['filename'] = filename.replace('.json', '')
                                config_data['name'] = filename.replace('.json', '').replace('.config', '')
                                configurations.append(config_data)
                        except Exception as e:
                            self.logger.error(f"Error reading config file {filename}: {e}")
            
            return configurations
            
        except Exception as e:
            self.logger.error(f"Error getting configurations: {e}")
            return []
    
    async def _bot_name_exists(self, bot_name: str) -> bool:
        """Check if bot name already exists as Docker container"""
        try:
            import subprocess
            result = subprocess.run([
                'docker', 'ps', '-a', '--format', '{{.Names}}'
            ], capture_output=True, text=True, check=True)
            
            existing_names = result.stdout.strip().split('\n')
            return bot_name in existing_names
            
        except Exception:
            return False
    
    async def _create_bot_container(self, bot_data: Dict) -> bool:
        """Create new bot container with the provided configuration"""
        try:
            # This would execute the bot.sh script with proper parameters
            # For now, return success simulation
            self.logger.info(f"Creating bot container: {bot_data}")
            
            # In real implementation, you would:
            # 1. Generate environment variables from bot_data
            # 2. Execute bot.sh with proper parameters
            # 3. Monitor container creation
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating bot container: {e}")
            return False
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for wizard navigation"""
        try:
            chat_id = query.message.chat_id
            session = self.session_manager.get_wizard_session(chat_id)
            
            if data.startswith("wiz_cred_"):
                cred_name = data.replace("wiz_cred_", "")
                session['data']['credentials_name'] = cred_name
                session['step'] = 1
                self.session_manager.set_wizard_session(chat_id, session)
                
                # Move to config selection
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self._show_config_selection(fake_update, fake_context)
                return True
            
            elif data.startswith("wiz_config_"):
                config_name = data.replace("wiz_config_", "")
                session['data']['config_name'] = config_name
                session['step'] = 2
                self.session_manager.set_wizard_session(chat_id, session)
                
                # Move to bot name input
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self._show_bot_name_input(fake_update, fake_context)
                return True
            
            elif data.startswith("wiz_name_"):
                bot_name = data.replace("wiz_name_", "")
                
                if await self._bot_name_exists(bot_name):
                    await query.edit_message_text(f"❌ Bot name `{bot_name}` already exists. Please choose a different name.")
                    return True
                
                session['data']['bot_name'] = bot_name
                session['step'] = 3
                self.session_manager.set_wizard_session(chat_id, session)
                
                # Move to symbol input
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self._show_symbol_input(fake_update, fake_context)
                return True
            
            elif data.startswith("wiz_symbol_"):
                symbol = data.replace("wiz_symbol_", "")
                session['data']['symbol'] = symbol
                session['step'] = 4
                self.session_manager.set_wizard_session(chat_id, session)
                
                # Move to amount input
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self._show_amount_input(fake_update, fake_context)
                return True
            
            elif data.startswith("wiz_amount_"):
                amount = data.replace("wiz_amount_", "")
                session['data']['amount'] = amount
                session['step'] = 5
                self.session_manager.set_wizard_session(chat_id, session)
                
                # Move to direction selection
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self._show_direction_selection(fake_update, fake_context)
                return True
            
            elif data.startswith("wiz_direction_"):
                direction = data.replace("wiz_direction_", "")
                direction_map = {
                    'long': 'Long Only',
                    'short': 'Short Only', 
                    'both': 'Both Directions'
                }
                session['data']['direction'] = direction_map.get(direction, direction)
                session['step'] = 6
                self.session_manager.set_wizard_session(chat_id, session)
                
                # Move to confirmation
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self._show_creation_confirmation(fake_update, fake_context)
                return True
            
            elif data == "wiz_create_confirm":
                await query.edit_message_text("🚀 Creating your trading bot...")
                
                success = await self._create_bot_container(session['data'])
                
                if success:
                    bot_name = session['data']['bot_name']
                    keyboard = self.create_keyboard([
                        [("📊 Check Status", f"bot_status_{bot_name}"), ("📜 View Logs", f"bot_logs_{bot_name}")],
                        [("🤖 Create Another", "bot_create_start"), ("❌ Close", "close")]
                    ])
                    
                    await query.edit_message_text(
                        f"✅ <b>Bot Created Successfully!</b>\n\n"
                        f"Bot Name: `{bot_name}`\n"
                        f"Status: Starting...\n\n"
                        f"🎉 Your trading bot is now running!\n"
                        f"💡 It may take a few moments to fully initialize.",
                        reply_markup=keyboard
                    )
                else:
                    await query.edit_message_text(
                        "❌ <b>Failed to create bot</b>\n\n"
                        "Please check your configuration and try again.\n\n"
                        "💡 Use `/createbot` to try again."
                    )
                
                # Clear wizard session
                self.session_manager.clear_wizard_session(chat_id)
                return True
            
            elif data == "wiz_back":
                # Handle back navigation
                current_step = session.get('step', 0)
                if current_step > 0:
                    session['step'] = current_step - 1
                    self.session_manager.set_wizard_session(chat_id, session)
                    
                    # Navigate to previous step
                    if current_step == 1:  # Back to credentials
                        from types import SimpleNamespace
                        fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                        fake_context = SimpleNamespace(bot=query.bot)
                        await self._show_credentials_selection(fake_update, fake_context)
                    elif current_step == 2:  # Back to config
                        from types import SimpleNamespace
                        fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                        fake_context = SimpleNamespace(bot=query.bot)
                        await self._show_config_selection(fake_update, fake_context)
                    # Add more back navigation as needed
                
                return True
            
            elif data == "bot_create_start":
                # Start new wizard
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self.handle_createbot_command(fake_update, fake_context)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in wizard callback handler: {e}")
            await query.edit_message_text(f"❌ Error: {str(e)}")
            return True 