#!/usr/bin/env python3
"""Main Telegram Command Handler with clean modular architecture."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

# Import modular handlers
from .base_handler import BaseTelegramHandler
from .credential_handler import CredentialHandler
from .bot_management_handler import BotManagementHandler
from .wizard_handler import WizardHandler

from ..templates import TelegramTemplates


class MainTelegramHandler(BaseTelegramHandler):
    """Main Telegram command handler with clean modular architecture."""
    
    def __init__(self, bot_token: str, chat_id: int):
        super().__init__(bot_token, chat_id)
        
        # Initialize specialized handlers
        self.credential_handler = CredentialHandler(bot_token, chat_id)
        self.bot_management_handler = BotManagementHandler(bot_token, chat_id)
        self.wizard_handler = WizardHandler(bot_token, chat_id)
    
    def start(self):
        """Start the Telegram bot with polling"""
        try:
            print("🔧 Creating Telegram application...")
            self.application = Application.builder().token(self.bot_token).build()

            print("📝 Adding command handlers...")
            self._setup_handlers()

            print("🚀 Starting bot...")
            self.application.run_polling(drop_pending_updates=True)
            
        except Exception as e:
            self.logger.error(f"Error starting bot: {e}")
            raise
    
    def _setup_handlers(self):
        """Setup all command handlers"""
        # Basic commands
        self.application.add_handler(CommandHandler("start", self.handle_start))
        self.application.add_handler(CommandHandler("help", self.handle_help))
        self.application.add_handler(CommandHandler("cancel", self.wizard_handler.handle_cancel))

        # Credential commands
        self.application.add_handler(CommandHandler("addcreds", self.credential_handler.handle_addcreds_wizard))
        self.application.add_handler(CommandHandler("listcreds", self.credential_handler.handle_listcreds))
        self.application.add_handler(CommandHandler("showcreds", self.credential_handler.handle_showcreds))
        self.application.add_handler(CommandHandler("deletecreds", self.credential_handler.handle_deletecreds))

        # Bot management commands
        self.application.add_handler(CommandHandler("createbot", self.wizard_handler.handle_createbot_wizard))
        self.application.add_handler(CommandHandler("startbot", self.bot_management_handler.handle_start_bot))
        self.application.add_handler(CommandHandler("list", self.bot_management_handler.handle_list_bots))
        self.application.add_handler(CommandHandler("status", self.bot_management_handler.handle_status_bot))
        self.application.add_handler(CommandHandler("stop", self.bot_management_handler.handle_stop_bot))
        self.application.add_handler(CommandHandler("stopall", self.bot_management_handler.handle_stop_all_bots))
        self.application.add_handler(CommandHandler("logs", self.bot_management_handler.handle_logs_bot))
        self.application.add_handler(CommandHandler("restart", self.bot_management_handler.handle_restart_bot))

        # Statistics and monitoring commands
        self.application.add_handler(CommandHandler("stats", self.bot_management_handler.handle_stats))
        self.application.add_handler(CommandHandler("performance", self.bot_management_handler.handle_performance))

        # Message handlers for wizard steps
        self.application.add_handler(MessageHandler(
            filters.TEXT & ~filters.COMMAND, 
            self.handle_message
        ))
        
        # Callback query handler
        self.application.add_handler(CallbackQueryHandler(self.handle_callback_query))
    
    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        # This method is required by TelegramBaseHandler but not used in our architecture
        # Commands are handled by specific handlers registered in _setup_handlers
        pass

    async def handle_start(self, update: Update, context) -> None:
        """Handle /start command with enhanced welcome message"""
        try:
            # Use enhanced welcome template
            template = TelegramTemplates.welcome_message()

            # Create inline keyboard
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            await update.message.reply_text(
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )

        except Exception as e:
            self.logger.error(f"Error in handle_start: {e}")
            # Fallback to simple message
            await update.message.reply_text(
                "🤖 **Welcome to AutoTrader Bot!**\n\n"
                "Use /help to see available commands.",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_help(self, update: Update, context) -> None:
        """Handle /help command with enhanced help system"""
        try:
            # Use enhanced help template
            template = TelegramTemplates.help_main()

            # Create inline keyboard
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            await update.message.reply_text(
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )

        except Exception as e:
            self.logger.error(f"Error in handle_help: {e}")
            # Fallback to simple help
            await update.message.reply_text(
                "📚 **Help**\n\n"
                "Use /addcreds to add credentials\n"
                "Use /createbot to create a trading bot\n"
                "Use /list to see all bots",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_message(self, update: Update, context) -> None:
        """Handle text messages (wizard steps)"""
        user_id = update.effective_user.id
        
        if not self._is_in_wizard(user_id):
            await self._send_info_message(
                update, 
                "Sử dụng `/help` để xem danh sách lệnh có sẵn"
            )
            return
        
        # Get current wizard step
        step = self._get_wizard_step(user_id)
        
        # Route to appropriate handler based on wizard type
        if step and step.startswith('addcreds_'):
            await self.credential_handler.handle_addcreds_wizard_step(update, context)
        elif step and step.startswith('createbot_'):
            await self.wizard_handler.handle_createbot_wizard_step(update, context)
        else:
            await self._send_error_message(update, "Wizard step không hợp lệ")
            self._clear_wizard(user_id)
    
    async def handle_callback_query(self, update: Update, context) -> None:
        """Handle callback queries from inline keyboards"""
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        # Route callback to appropriate handler
        if data.startswith("help_"):
            await self._handle_help_callback(query, data)
        elif data.startswith("createbot_"):
            await self._handle_createbot_callback(query, data)
        elif data.startswith("stop_"):
            await self._handle_stop_callback(query, data)
        elif data.startswith("delete_creds_"):
            await self._handle_delete_creds_callback(query, data)
        elif data.startswith("stopall_"):
            await self._handle_stopall_callback(query, data)
        elif data.startswith("bot_") or data.startswith("creds_"):
            await self._handle_generic_callback(query, data)
        else:
            await query.edit_message_text("❌ Callback không hợp lệ")
    
    async def _handle_help_callback(self, query, data: str) -> None:
        """Handle help-related callbacks with enhanced templates"""
        try:
            template = None

            if data == "help_main":
                template = TelegramTemplates.help_main()
            elif data == "help_credentials":
                template = TelegramTemplates.help_credentials()
            elif data == "help_bots":
                template = TelegramTemplates.help_bots()
            elif data == "help_monitoring":
                template = TelegramTemplates.help_monitoring()
            elif data == "help_getting_started":
                template = TelegramTemplates.help_getting_started()
            elif data == "help_faq":
                template = TelegramTemplates.help_faq()
            else:
                await query.edit_message_text(
                    "❌ Tùy chọn không hợp lệ",
                    parse_mode=ParseMode.HTML
                )
                return

            if template:
                keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                await query.edit_message_text(
                    template.content,
                    parse_mode=ParseMode.HTML,
                    reply_markup=keyboard
                )

        except Exception as e:
            self.logger.error(f"Error in help callback: {e}")
            await query.edit_message_text(
                "❌ Có lỗi xảy ra khi hiển thị help",
                parse_mode=ParseMode.HTML
            )

    async def _handle_stopall_callback(self, query, data: str) -> None:
        """Handle stopall confirmation callbacks"""
        try:
            if data == "stopall_confirm":
                await self.bot_management_handler._execute_stop_all(query)
            elif data == "stopall_cancel":
                await query.edit_message_text(
                    "❌ **Stop All Cancelled**\n\nNo bots were stopped.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    "❌ Invalid stopall callback",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            self.logger.error(f"Error in stopall callback: {e}")
            await query.edit_message_text(
                "❌ Error processing stopall request",
                parse_mode=ParseMode.HTML
            )

    async def _handle_generic_callback(self, query, data: str) -> None:
        """Handle generic callbacks for bot and credential actions"""
        try:
            if data.startswith("bot_"):
                # Bot-related callbacks
                if data == "bot_list":
                    # Refresh bot list
                    all_containers = []  # Would get from bot_management_handler
                    template = TelegramTemplates.bot_list_enhanced(all_containers)
                    keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                    await query.edit_message_text(
                        template.content,
                        parse_mode=ParseMode.HTML,
                        reply_markup=keyboard
                    )
                elif data == "bot_create":
                    # Start create bot wizard
                    await query.edit_message_text(
                        "🚀 Starting bot creation wizard...\n\nUse /createbot command to begin.",
                        parse_mode=ParseMode.HTML
                    )
                elif data == "bot_stats":
                    await query.edit_message_text(
                        "📊 Use /stats command to view detailed statistics.",
                        parse_mode=ParseMode.HTML
                    )
                else:
                    await query.edit_message_text(
                        "❌ Bot callback not implemented yet",
                        parse_mode=ParseMode.HTML
                    )

            elif data.startswith("creds_"):
                # Credential-related callbacks
                if data == "creds_add":
                    await query.edit_message_text(
                        "🔑 Starting credential wizard...\n\nUse /addcreds command to begin.",
                        parse_mode=ParseMode.HTML
                    )
                elif data == "creds_list":
                    await query.edit_message_text(
                        "📋 Use /listcreds command to view credentials.",
                        parse_mode=ParseMode.HTML
                    )
                else:
                    await query.edit_message_text(
                        "❌ Credential callback not implemented yet",
                        parse_mode=ParseMode.HTML
                    )
            else:
                await query.edit_message_text(
                    "❌ Unknown callback",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            self.logger.error(f"Error in generic callback: {e}")
            await query.edit_message_text(
                "❌ Error processing callback",
                parse_mode=ParseMode.HTML
            )
    
    async def _handle_createbot_callback(self, query, data: str) -> None:
        """Handle createbot-related callbacks"""
        if data.startswith("createbot_profile_"):
            profile = data.replace("createbot_profile_", "")
            await self.wizard_handler.handle_createbot_profile_callback(query, profile)
        elif data.startswith("createbot_direction_"):
            direction = data.replace("createbot_direction_", "")
            await self.wizard_handler.handle_createbot_direction_callback(query, direction)
        elif data.startswith("createbot_testmode_"):
            test_mode = data.replace("createbot_testmode_", "")
            await self.wizard_handler.handle_createbot_testmode_callback(query, test_mode)
        elif data == "createbot_confirm":
            await self.wizard_handler.handle_createbot_confirm(query)
        elif data == "createbot_cancel":
            user_id = query.from_user.id
            self._clear_wizard(user_id)
            await query.edit_message_text("❌ **Đã hủy tạo bot**", parse_mode=ParseMode.MARKDOWN)
    
    async def _handle_stop_callback(self, query, data: str) -> None:
        """Handle stop-related callbacks"""
        if data.startswith("stop_confirm_"):
            symbol = data.replace("stop_confirm_", "")
            await self.bot_management_handler.handle_stop_confirm(query, symbol)
        elif data == "stop_cancel":
            await query.edit_message_text("❌ **Đã hủy dừng bot**", parse_mode=ParseMode.MARKDOWN)
    
    async def _handle_delete_creds_callback(self, query, data: str) -> None:
        """Handle credential deletion callbacks"""
        if data.startswith("delete_creds_confirm_"):
            profile = data.replace("delete_creds_confirm_", "")
            await self.credential_handler.handle_delete_creds_callback(query, profile)
        elif data == "delete_creds_cancel":
            await query.edit_message_text("❌ **Đã hủy xóa tài khoản**", parse_mode=ParseMode.MARKDOWN)
    
    async def _handle_stopall_callback(self, query, data: str) -> None:
        """Handle stopall callbacks"""
        if data == "confirm_stopall":
            await self.bot_management_handler.handle_stopall_confirm(query)
        elif data == "cancel_stopall":
            await query.edit_message_text("❌ **Đã hủy dừng tất cả bot**", parse_mode=ParseMode.MARKDOWN)


# For backward compatibility
ImprovedTelegramCommandHandler = MainTelegramHandler
