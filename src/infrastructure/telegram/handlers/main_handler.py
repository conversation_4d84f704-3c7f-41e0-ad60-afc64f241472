#!/usr/bin/env python3
"""Main Telegram Command Handler with clean modular architecture."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

# Import modular handlers
from .base_handler import BaseTelegramHandler
from .credential_handler import CredentialHandler
from .bot_management_handler import BotManagementHandler
from .wizard_handler import WizardHandler

from ..templates import TelegramTemplates


class MainTelegramHandler(BaseTelegramHandler):
    """Main Telegram command handler with clean modular architecture."""
    
    def __init__(self, bot_token: str, chat_id: int):
        super().__init__(bot_token, chat_id)

        # Initialize specialized handlers with error handling
        try:
            print("   - Initializing CredentialHandler...")
            self.credential_handler = CredentialHandler(bot_token, chat_id)
            print("   - Initializing BotManagementHandler...")
            self.bot_management_handler = BotManagementHandler(bot_token, chat_id)
            print("   - Initializing WizardHandler...")
            self.wizard_handler = WizardHandler(bot_token, chat_id)
            print("   - All handlers initialized successfully")
        except Exception as e:
            print(f"   ❌ Error initializing handlers: {e}")
            self.logger.error(f"Error initializing handlers: {e}", exc_info=True)
            # Create dummy handlers to prevent crashes
            self.credential_handler = None
            self.bot_management_handler = None
            self.wizard_handler = None
    
    def start(self):
        """Start the Telegram bot with polling"""
        try:
            print("🔧 Creating Telegram application...")
            self.application = Application.builder().token(self.bot_token).build()

            print("📝 Adding command handlers...")
            self._setup_handlers()

            print("🚀 Starting bot...")
            print(f"   Application: {self.application}")
            print(f"   Handlers registered: {len(self.application.handlers)}")

            # Debug handler details
            for group_id, handler_group in self.application.handlers.items():
                print(f"   Handler group {group_id}: {len(handler_group)} handlers")
                for j, handler in enumerate(handler_group):
                    print(f"     Handler {j}: {type(handler).__name__}")

            # Add debug for polling
            print("   Starting polling...")
            print(f"   Drop pending updates: False")
            print(f"   Allowed updates: ['message', 'callback_query']")

            # Add custom error handler
            async def error_handler(update, context):
                print(f"🚨 ERROR: {context.error}")
                self.logger.error(f"Update {update} caused error {context.error}")

            self.application.add_error_handler(error_handler)

            # Add debug for application events
            async def post_init(application):
                print("🚀 Application post_init called")

            async def post_shutdown(application):
                print("🛑 Application post_shutdown called")

            self.application.post_init = post_init
            self.application.post_shutdown = post_shutdown

            print("   Calling run_polling...")
            self.application.run_polling(
                drop_pending_updates=False,
                allowed_updates=["message", "callback_query"],
                poll_interval=1.0,
                timeout=10
            )
            print("   run_polling completed")

        except Exception as e:
            self.logger.error(f"Error starting bot: {e}", exc_info=True)
            raise
    
    def _setup_handlers(self):
        """Setup all command handlers"""
        print("📝 Setting up command handlers...")

        # Basic commands
        print("   - Adding /start handler")
        self.application.add_handler(CommandHandler("start", self.handle_start))
        print("   - Adding /help handler")
        self.application.add_handler(CommandHandler("help", self.handle_help))
        print("   - Adding /cancel handler")
        if self.wizard_handler:
            self.application.add_handler(CommandHandler("cancel", self.wizard_handler.handle_cancel))
        else:
            print("   ⚠️ Wizard handler not available for cancel")

        # Add test handler
        print("   - Adding /test handler")
        self.application.add_handler(CommandHandler("test", self.handle_test))

        # Credential commands
        if self.credential_handler:
            print("   - Adding credential handlers")
            self.application.add_handler(CommandHandler("addcreds", self.credential_handler.handle_addcreds_wizard))
            self.application.add_handler(CommandHandler("listcreds", self.credential_handler.handle_listcreds))
            self.application.add_handler(CommandHandler("showcreds", self.credential_handler.handle_showcreds))
            self.application.add_handler(CommandHandler("deletecreds", self.credential_handler.handle_deletecreds))
        else:
            print("   ⚠️ Credential handler not available")

        # Bot management commands
        if self.bot_management_handler and self.wizard_handler:
            print("   - Adding bot management handlers")
            self.application.add_handler(CommandHandler("createbot", self.wizard_handler.handle_createbot_wizard))
            self.application.add_handler(CommandHandler("startbot", self.bot_management_handler.handle_start_bot))
            self.application.add_handler(CommandHandler("list", self.bot_management_handler.handle_list_bots))
            self.application.add_handler(CommandHandler("status", self.bot_management_handler.handle_status_bot))
            self.application.add_handler(CommandHandler("stop", self.bot_management_handler.handle_stop_bot))
            self.application.add_handler(CommandHandler("stopall", self.bot_management_handler.handle_stop_all_bots))
            self.application.add_handler(CommandHandler("logs", self.bot_management_handler.handle_logs_bot))
            self.application.add_handler(CommandHandler("restart", self.bot_management_handler.handle_restart_bot))
        else:
            print("   ⚠️ Bot management handlers not available")



        # Message handlers for wizard steps
        print("   - Adding message handler")
        self.application.add_handler(MessageHandler(
            filters.TEXT & ~filters.COMMAND,
            self.handle_message
        ))

        # Callback query handler
        print("   - Adding callback query handler")
        self.application.add_handler(CallbackQueryHandler(self.handle_callback_query))

        # Add debug handler for all updates
        print("   - Adding debug update handler")
        self.application.add_handler(MessageHandler(filters.ALL, self.debug_update_handler), group=999)
    
    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        # This method is required by TelegramBaseHandler but not used in our architecture
        # Commands are handled by specific handlers registered in _setup_handlers
        pass

    async def handle_start(self, update: Update, context) -> None:
        """Handle /start command with enhanced welcome message"""
        self.logger.info(f"handle_start called by user {update.effective_user.id}")
        try:
            # Use enhanced welcome template
            self.logger.info("Getting welcome message template")
            template = TelegramTemplates.welcome_message()

            # Create inline keyboard
            self.logger.info("Creating inline keyboard")
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            self.logger.info("Sending welcome message")
            await update.message.reply_text(
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )
            self.logger.info("Welcome message sent successfully")

        except Exception as e:
            self.logger.error(f"Error in handle_start: {e}", exc_info=True)
            # Fallback to simple message
            try:
                await update.message.reply_text(
                    "🤖 **Welcome to AutoTrader Bot!**\n\n"
                    "Use /help to see available commands.",
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e2:
                self.logger.error(f"Error in fallback message: {e2}", exc_info=True)
    
    async def handle_help(self, update: Update, context) -> None:
        """Handle /help command with enhanced help system"""
        self.logger.info(f"handle_help called by user {update.effective_user.id}")
        try:
            # Use enhanced help template
            self.logger.info("Getting help template")
            template = TelegramTemplates.help_main()

            # Create inline keyboard
            self.logger.info("Creating help keyboard")
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            self.logger.info("Sending help message")
            await update.message.reply_text(
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )
            self.logger.info("Help message sent successfully")

        except Exception as e:
            self.logger.error(f"Error in handle_help: {e}", exc_info=True)
            # Fallback to simple help
            try:
                await update.message.reply_text(
                    "📚 **Help**\n\n"
                    "Use /addcreds to add credentials\n"
                    "Use /createbot to create a trading bot\n"
                    "Use /list to see all bots",
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e2:
                self.logger.error(f"Error in fallback help: {e2}", exc_info=True)

    async def handle_test(self, update: Update, context) -> None:
        """Simple test handler to verify bot is working"""
        self.logger.info(f"handle_test called by user {update.effective_user.id}")
        try:
            await update.message.reply_text(
                "✅ **Test Successful!**\n\n"
                f"User ID: {update.effective_user.id}\n"
                f"Chat ID: {update.effective_chat.id}\n"
                f"Message: {update.message.text}",
                parse_mode=ParseMode.MARKDOWN
            )
            self.logger.info("Test message sent successfully")
        except Exception as e:
            self.logger.error(f"Error in handle_test: {e}", exc_info=True)

    async def debug_update_handler(self, update: Update, context) -> None:
        """Debug handler to log all updates"""
        print(f"🔍 DEBUG: Received update: {update.update_id}")
        print(f"   Update type: {type(update)}")
        print(f"   Update dict: {update.to_dict()}")

        if update.message:
            print(f"   Message from user {update.effective_user.id}: {update.message.text}")
            print(f"   Chat ID: {update.effective_chat.id}")
            print(f"   Message ID: {update.message.message_id}")
        if update.callback_query:
            print(f"   Callback query from user {update.effective_user.id}: {update.callback_query.data}")

        self.logger.info(f"Debug update: {update.update_id} from user {update.effective_user.id}")

        # Always continue processing
        return
    
    async def handle_message(self, update: Update, context) -> None:
        """Handle text messages (wizard steps)"""
        user_id = update.effective_user.id
        
        if not self._is_in_wizard(user_id):
            await self._send_info_message(
                update, 
                "Sử dụng `/help` để xem danh sách lệnh có sẵn"
            )
            return
        
        # Get current wizard step
        step = self._get_wizard_step(user_id)
        
        # Route to appropriate handler based on wizard type
        if step and step.startswith('addcreds_'):
            await self.credential_handler.handle_addcreds_wizard_step(update, context)
        elif step and step.startswith('createbot_'):
            await self.wizard_handler.handle_createbot_wizard_step(update, context)
        else:
            await self._send_error_message(update, "Wizard step không hợp lệ")
            self._clear_wizard(user_id)
    
    async def handle_callback_query(self, update: Update, context) -> None:
        """Handle callback queries from inline keyboards"""
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        # Route callback to appropriate handler
        if data.startswith("help_"):
            await self._handle_help_callback(query, data)
        elif data.startswith("createbot_"):
            await self._handle_createbot_callback(query, data)
        elif data.startswith("stop_"):
            await self._handle_stop_callback(query, data)
        elif data.startswith("delete_creds_"):
            await self._handle_delete_creds_callback(query, data)
        elif data.startswith("stopall_"):
            await self._handle_stopall_callback(query, data)
        elif data.startswith("bot_") or data.startswith("creds_"):
            await self._handle_generic_callback(query, data)
        else:
            await query.edit_message_text("❌ Callback không hợp lệ")
    
    async def _handle_help_callback(self, query, data: str) -> None:
        """Handle help-related callbacks with enhanced templates"""
        try:
            template = None

            if data == "help_main":
                template = TelegramTemplates.help_main()
            elif data == "help_credentials":
                template = TelegramTemplates.help_credentials()
            elif data == "help_bots":
                template = TelegramTemplates.help_bots()
            elif data == "help_monitoring":
                template = TelegramTemplates.help_monitoring()
            elif data == "help_getting_started":
                template = TelegramTemplates.help_getting_started()
            elif data == "help_faq":
                template = TelegramTemplates.help_faq()
            else:
                await query.edit_message_text(
                    "❌ Tùy chọn không hợp lệ",
                    parse_mode=ParseMode.HTML
                )
                return

            if template:
                keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                await query.edit_message_text(
                    template.content,
                    parse_mode=ParseMode.HTML,
                    reply_markup=keyboard
                )

        except Exception as e:
            self.logger.error(f"Error in help callback: {e}")
            await query.edit_message_text(
                "❌ Có lỗi xảy ra khi hiển thị help",
                parse_mode=ParseMode.HTML
            )

    async def _handle_stopall_callback(self, query, data: str) -> None:
        """Handle stopall confirmation callbacks"""
        try:
            if data == "stopall_confirm":
                await self.bot_management_handler._execute_stop_all(query)
            elif data == "stopall_cancel":
                await query.edit_message_text(
                    "❌ **Stop All Cancelled**\n\nNo bots were stopped.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    "❌ Invalid stopall callback",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            self.logger.error(f"Error in stopall callback: {e}")
            await query.edit_message_text(
                "❌ Error processing stopall request",
                parse_mode=ParseMode.HTML
            )

    async def _handle_generic_callback(self, query, data: str) -> None:
        """Handle generic callbacks for bot and credential actions"""
        try:
            if data.startswith("bot_"):
                # Bot-related callbacks
                if data == "bot_list":
                    # Refresh bot list
                    all_containers = []  # Would get from bot_management_handler
                    template = TelegramTemplates.bot_list_enhanced(all_containers)
                    keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                    await query.edit_message_text(
                        template.content,
                        parse_mode=ParseMode.HTML,
                        reply_markup=keyboard
                    )
                elif data == "bot_create":
                    # Start create bot wizard
                    await query.edit_message_text(
                        "🚀 Starting bot creation wizard...\n\nUse /createbot command to begin.",
                        parse_mode=ParseMode.HTML
                    )
                elif data == "bot_stats":
                    await query.edit_message_text(
                        "📊 Use /stats command to view detailed statistics.",
                        parse_mode=ParseMode.HTML
                    )
                else:
                    await query.edit_message_text(
                        "❌ Bot callback not implemented yet",
                        parse_mode=ParseMode.HTML
                    )

            elif data.startswith("creds_"):
                # Credential-related callbacks
                if data == "creds_add":
                    await query.edit_message_text(
                        "🔑 **Starting Credential Wizard**\n\n"
                        "Please use the command: /addcreds\n\n"
                        "This will start the interactive credential setup wizard.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                elif data == "creds_list":
                    await query.edit_message_text(
                        "📋 **View Credentials**\n\n"
                        "Please use the command: /listcreds\n\n"
                        "This will show all your stored credential profiles.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    await query.edit_message_text(
                        "❌ Credential callback not implemented yet",
                        parse_mode=ParseMode.HTML
                    )
            else:
                await query.edit_message_text(
                    "❌ Unknown callback",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            self.logger.error(f"Error in generic callback: {e}")
            await query.edit_message_text(
                "❌ Error processing callback",
                parse_mode=ParseMode.HTML
            )
    
    async def _handle_createbot_callback(self, query, data: str) -> None:
        """Handle createbot-related callbacks"""
        if data.startswith("createbot_profile_"):
            profile = data.replace("createbot_profile_", "")
            await self.wizard_handler.handle_createbot_profile_callback(query, profile)
        elif data.startswith("createbot_direction_"):
            direction = data.replace("createbot_direction_", "")
            await self.wizard_handler.handle_createbot_direction_callback(query, direction)
        elif data.startswith("createbot_testmode_"):
            test_mode = data.replace("createbot_testmode_", "")
            await self.wizard_handler.handle_createbot_testmode_callback(query, test_mode)
        elif data == "createbot_confirm":
            await self.wizard_handler.handle_createbot_confirm(query)
        elif data == "createbot_cancel":
            user_id = query.from_user.id
            self._clear_wizard(user_id)
            await query.edit_message_text("❌ **Đã hủy tạo bot**", parse_mode=ParseMode.MARKDOWN)
    
    async def _handle_stop_callback(self, query, data: str) -> None:
        """Handle stop-related callbacks"""
        if data.startswith("stop_confirm_"):
            symbol = data.replace("stop_confirm_", "")
            await self.bot_management_handler.handle_stop_confirm(query, symbol)
        elif data == "stop_cancel":
            await query.edit_message_text("❌ **Đã hủy dừng bot**", parse_mode=ParseMode.MARKDOWN)
    
    async def _handle_delete_creds_callback(self, query, data: str) -> None:
        """Handle credential deletion callbacks"""
        if data.startswith("delete_creds_confirm_"):
            profile = data.replace("delete_creds_confirm_", "")
            await self.credential_handler.handle_delete_creds_callback(query, profile)
        elif data == "delete_creds_cancel":
            await query.edit_message_text("❌ **Đã hủy xóa tài khoản**", parse_mode=ParseMode.MARKDOWN)
    
    async def _handle_stopall_callback(self, query, data: str) -> None:
        """Handle stopall callbacks"""
        if data == "confirm_stopall":
            await self.bot_management_handler.handle_stopall_confirm(query)
        elif data == "cancel_stopall":
            await query.edit_message_text("❌ **Đã hủy dừng tất cả bot**", parse_mode=ParseMode.MARKDOWN)


# For backward compatibility
ImprovedTelegramCommandHandler = MainTelegramHandler
