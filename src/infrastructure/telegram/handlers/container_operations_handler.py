#!/usr/bin/env python3
"""Container Operations Handler - Handle stop, restart, remove operations"""

from typing import Dict, List, Optional, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from ..telegram_base import Telegram<PERSON>ase<PERSON>and<PERSON>
from ....utils.container_helper import container_helper


class ContainerOperationsHandler(TelegramBaseHandler):
    """Handles container operations like stop, restart, remove"""

    def __init__(self, session_manager):
        super().__init__('ContainerOperationsHandler')
        self.session_manager = session_manager

    async def handle_command(self, update, context, command: str) -> bool:
        """Handle container operation commands"""
        # This handler doesn't use the generic handle_command pattern
        # Commands are registered directly in central manager
        return False
    
    # ===============================
    # Container Operation Commands
    # ===============================
    
    async def handle_stop_command(self, update, context):
        """Handle /stop command - Stop a trading bot"""
        try:
            if not context.args:
                await self._show_stop_help(update, context)
                return
            
            symbol_or_container = context.args[0]
            
            # Find container by symbol
            container_name = container_helper.find_container_by_symbol(symbol_or_container)
            
            if not container_name:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ No container found for `{symbol_or_container}`\n\n"
                    f"💡 Available containers:\n"
                    f"{self._format_available_containers()}\n\n"
                    "Use `/list` to see detailed information."
                )
                return
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("⏹️ Confirm Stop", f"op_stop_confirm_{container_name}"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"⏹️ <b>Stop Container</b>\n\n"
                f"Are you sure you want to stop `{container_name}`?\n\n"
                "⚠️ This will stop all trading activities for this bot.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "stop")
            )
    
    async def handle_restart_command(self, update, context):
        """Handle /restart command - Restart a trading bot"""
        try:
            if not context.args:
                await self._show_restart_help(update, context)
                return
            
            symbol_or_container = context.args[0]
            
            # Find container by symbol
            container_name = container_helper.find_container_by_symbol(symbol_or_container)
            
            if not container_name:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ No container found for `{symbol_or_container}`\n\n"
                    f"💡 Available containers:\n"
                    f"{self._format_available_containers()}\n\n"
                    "Use `/list` to see detailed information."
                )
                return
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("🔄 Confirm Restart", f"op_restart_confirm_{container_name}"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"🔄 <b>Restart Container</b>\n\n"
                f"Are you sure you want to restart `{container_name}`?\n\n"
                "⚠️ This will temporarily interrupt trading activities.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "restart")
            )
    
    async def handle_remove_command(self, update, context):
        """Handle /remove command - Remove a trading bot container"""
        try:
            if not context.args:
                await self._show_remove_help(update, context)
                return
            
            symbol_or_container = context.args[0]
            
            # Find container by symbol
            container_name = container_helper.find_container_by_symbol(symbol_or_container)
            
            if not container_name:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ No container found for `{symbol_or_container}`\n\n"
                    f"💡 Available containers:\n"
                    f"{self._format_available_containers()}\n\n"
                    "Use `/list` to see detailed information."
                )
                return
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("🗑️ Confirm Remove", f"op_remove_confirm_{container_name}"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"🗑️ <b>Remove Container</b>\n\n"
                f"Are you sure you want to remove `{container_name}`?\n\n"
                "⚠️ This will permanently delete the container and all its data.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "remove")
            )
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for container operations"""
        try:
            if data.startswith("op_stop_confirm_"):
                container_name = data.replace("op_stop_confirm_", "")
                success, message = container_helper.stop_container(container_name)
                
                if success:
                    await query.edit_message_text(
                        f"✅ <b>Container Stopped</b>\n\n{message}",
                        parse_mode=ParseMode.HTML
                    )
                else:
                    await query.edit_message_text(
                        f"❌ <b>Stop Failed</b>\n\n{message}",
                        parse_mode=ParseMode.HTML
                    )
                return True
            
            elif data.startswith("op_restart_confirm_"):
                container_name = data.replace("op_restart_confirm_", "")
                success, message = container_helper.restart_container(container_name)
                
                if success:
                    await query.edit_message_text(
                        f"✅ <b>Container Restarted</b>\n\n{message}",
                        parse_mode=ParseMode.HTML
                    )
                else:
                    await query.edit_message_text(
                        f"❌ <b>Restart Failed</b>\n\n{message}",
                        parse_mode=ParseMode.HTML
                    )
                return True
            
            elif data.startswith("op_remove_confirm_"):
                container_name = data.replace("op_remove_confirm_", "")
                success, message = container_helper.remove_container(container_name)
                
                if success:
                    await query.edit_message_text(
                        f"✅ <b>Container Removed</b>\n\n{message}",
                        parse_mode=ParseMode.HTML
                    )
                else:
                    await query.edit_message_text(
                        f"❌ <b>Remove Failed</b>\n\n{message}",
                        parse_mode=ParseMode.HTML
                    )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in callback handler: {e}")
            await query.edit_message_text(f"❌ Error: {str(e)}")
            return True
    
    # ===============================
    # Helper Methods
    # ===============================
    
    def _format_available_containers(self) -> str:
        """Format available containers list"""
        containers = container_helper.list_trading_containers()
        if not containers:
            return "No containers available"
        
        result = ""
        for container in containers[:5]:  # Show max 5
            status = "🟢" if container['running'] else "🔴"
            result += f"{status} `{container['name']}`\n"
        
        if len(containers) > 5:
            result += f"... and {len(containers) - 5} more"
        
        return result
    
    async def _show_stop_help(self, update, context):
        """Show help for stop command"""
        containers = container_helper.list_trading_containers()
        running_containers = [c for c in containers if c['running']]
        
        message = "⏹️ <b>Stop Container Command</b>\n\n"
        message += "<b>Usage:</b> `/stop <symbol_or_container>`\n\n"
        
        if running_containers:
            message += "<b>Running containers:</b>\n"
            for container in running_containers[:5]:
                message += f"🟢 `{container['name']}`\n"
            message += "\n<b>Example:</b> `/stop eth` or `/stop eth_trader`"
        else:
            message += "❌ No running containers found.\n\n"
            message += "Use `/list` to see all containers."
        
        await self.send_message(
            context.bot,
            update.effective_chat.id,
            message
        )
    
    async def _show_restart_help(self, update, context):
        """Show help for restart command"""
        containers = container_helper.list_trading_containers()
        
        message = "🔄 <b>Restart Container Command</b>\n\n"
        message += "<b>Usage:</b> `/restart <symbol_or_container>`\n\n"
        
        if containers:
            message += "<b>Available containers:</b>\n"
            for container in containers[:5]:
                status = "🟢" if container['running'] else "🔴"
                message += f"{status} `{container['name']}`\n"
            message += "\n<b>Example:</b> `/restart eth` or `/restart eth_trader`"
        else:
            message += "❌ No containers found.\n\n"
            message += "Use `/createbot` to create a new trading bot."
        
        await self.send_message(
            context.bot,
            update.effective_chat.id,
            message
        )
    
    async def _show_remove_help(self, update, context):
        """Show help for remove command"""
        containers = container_helper.list_trading_containers()
        
        message = "🗑️ <b>Remove Container Command</b>\n\n"
        message += "<b>Usage:</b> `/remove <symbol_or_container>`\n\n"
        message += "⚠️ <b>Warning:</b> This permanently deletes the container!\n\n"
        
        if containers:
            message += "<b>Available containers:</b>\n"
            for container in containers[:5]:
                status = "🟢" if container['running'] else "🔴"
                message += f"{status} `{container['name']}`\n"
            message += "\n<b>Example:</b> `/remove eth` or `/remove eth_trader`"
        else:
            message += "❌ No containers found."
        
        await self.send_message(
            context.bot,
            update.effective_chat.id,
            message
        )
