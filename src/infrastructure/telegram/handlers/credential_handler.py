#!/usr/bin/env python3
"""Credential management handler for Telegram bot."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.constants import ParseMode

from .base_handler import BaseTelegramHandler
from ....core.credential_utils import list_profiles, load_credentials, store_credentials


class CredentialHandler(BaseTelegramHandler):
    """Handler for credential management operations."""

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass
    
    async def handle_listcreds(self, update: Update, context) -> None:
        """Handle /listcreds command using centralized utilities"""
        self.logger.info(f"Received /listcreds command from user {update.effective_user.id}")

        try:
            # Use centralized credential utilities
            profiles = list_profiles()
            
            if not profiles:
                await update.message.reply_text(
                    "📋 **Không có tài khoản nào**\n\n"
                    "Sử dụng `/addcreds` để thêm tài khoản mới.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            
            # Format profiles list
            message = "📋 **Danh sách tài khoản:**\n\n"
            for profile in profiles:
                display_name = profile.get('display_name', profile['profile'])
                message += f"🔑 **{profile['profile']}**\n"
                message += f"   Tên hiển thị: {display_name}\n"
                message += f"   Định dạng: {profile.get('format', 'json')}\n\n"
            
            message += f"📊 **Tổng cộng:** {len(profiles)} tài khoản"
            
            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error in handle_listcreds: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_addcreds_wizard(self, update: Update, context) -> None:
        """Handle /addcreds command - start credential wizard"""
        user_id = update.effective_user.id
        
        # Clear any existing wizard state
        self._clear_wizard(user_id)
        
        # Start credential wizard
        self._set_wizard_step(user_id, 'addcreds_profile')
        
        await update.message.reply_text(
            "🔑 **Thêm tài khoản mới**\n\n"
            "Nhập tên profile (ví dụ: main, test, backup):",
            reply_markup=ForceReply(selective=True),
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_addcreds_wizard_step(self, update: Update, context) -> None:
        """Handle steps in add credentials wizard"""
        user_id = update.effective_user.id
        step = self._get_wizard_step(user_id)
        text = update.message.text.strip()
        
        if step == 'addcreds_profile':
            # Validate profile name
            if not text or len(text) < 2:
                await self._send_error_message(update, "Tên profile phải có ít nhất 2 ký tự")
                return
            
            # Check if profile already exists
            profiles = list_profiles()
            if any(p['profile'] == text for p in profiles):
                await self._send_error_message(update, f"Profile '{text}' đã tồn tại")
                return
            
            # Save profile name and move to next step
            self._set_session_data(user_id, 'addcreds_profile', text)
            self._set_wizard_step(user_id, 'addcreds_api_key')
            
            await update.message.reply_text(
                f"✅ Profile: `{text}`\n\n"
                "Nhập API Key:",
                reply_markup=ForceReply(selective=True),
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif step == 'addcreds_api_key':
            # Validate API key
            if not self._validate_api_key(text):
                await self._send_error_message(update, "API Key không hợp lệ (phải có ít nhất 10 ký tự)")
                return
            
            # Save API key and move to next step
            self._set_session_data(user_id, 'addcreds_api_key', text)
            self._set_wizard_step(user_id, 'addcreds_api_secret')
            
            await update.message.reply_text(
                "✅ API Key đã lưu\n\n"
                "Nhập API Secret:",
                reply_markup=ForceReply(selective=True),
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif step == 'addcreds_api_secret':
            # Validate API secret
            if not text or len(text) < 10:
                await self._send_error_message(update, "API Secret không hợp lệ (phải có ít nhất 10 ký tự)")
                return
            
            # Save API secret and move to next step
            self._set_session_data(user_id, 'addcreds_api_secret', text)
            self._set_wizard_step(user_id, 'addcreds_display_name')
            
            await update.message.reply_text(
                "✅ API Secret đã lưu\n\n"
                "Nhập tên hiển thị (hoặc gửi 'skip' để bỏ qua):",
                reply_markup=ForceReply(selective=True),
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif step == 'addcreds_display_name':
            # Handle display name (optional)
            display_name = None if text.lower() == 'skip' else text
            
            # Get all saved data
            profile = self._get_session_data(user_id, 'addcreds_profile')
            api_key = self._get_session_data(user_id, 'addcreds_api_key')
            api_secret = self._get_session_data(user_id, 'addcreds_api_secret')
            
            try:
                # Store credentials using centralized utility
                store_credentials(profile, api_key, api_secret, display_name)
                
                # Clear wizard state
                self._clear_wizard(user_id)
                
                await self._send_success_message(
                    update, 
                    f"Tài khoản '{profile}' đã được thêm thành công!"
                )
                
            except Exception as e:
                self.logger.error(f"Error storing credentials: {e}")
                await self._send_error_message(update, f"Không thể lưu tài khoản: {str(e)}")
                self._clear_wizard(user_id)
    
    async def handle_deletecreds(self, update: Update, context) -> None:
        """Handle /deletecreds command"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /deletecreds <profile_name>")
            return
        
        profile = context.args[0]
        
        try:
            # Check if profile exists
            profiles = list_profiles()
            if not any(p['profile'] == profile for p in profiles):
                await self._send_error_message(update, f"Profile '{profile}' không tồn tại")
                return
            
            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Xác nhận", callback_data=f"delete_creds_confirm_{profile}"),
                    InlineKeyboardButton("❌ Hủy", callback_data="delete_creds_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                f"⚠️ **Xác nhận xóa tài khoản**\n\n"
                f"Bạn có chắc muốn xóa profile `{profile}`?\n"
                f"Hành động này không thể hoàn tác!",
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"Error in handle_deletecreds: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_delete_creds_callback(self, query, profile: str) -> None:
        """Handle credential deletion confirmation"""
        try:
            # Delete credential file
            result = await self._execute_botsh_command([
                self.bot_script_path, "delete-credentials", profile, "--force"
            ])
            
            if result[0] == 0:
                await query.edit_message_text(
                    f"✅ **Đã xóa tài khoản '{profile}' thành công**",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Lỗi xóa tài khoản:** {result[2] or result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
                
        except Exception as e:
            self.logger.error(f"Error deleting credentials: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_showcreds(self, update: Update, context) -> None:
        """Handle /showcreds command"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /showcreds <profile_name>")
            return
        
        profile = context.args[0]
        
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "show-credentials", profile
            ])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"```\n{result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error in handle_showcreds: {e}")
            await self._send_error_message(update, str(e))
