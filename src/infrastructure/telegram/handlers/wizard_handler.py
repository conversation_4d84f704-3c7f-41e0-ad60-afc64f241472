#!/usr/bin/env python3
"""Wizard handler for Telegram bot operations."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.constants import ParseMode

from .base_handler import BaseTelegramHandler
from ....core.credential_utils import list_profiles


class WizardHandler(BaseTelegramHandler):
    """Handler for wizard-based operations."""

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass
    
    async def handle_createbot_wizard(self, update: Update, context) -> None:
        """Handle /createbot command - start bot creation wizard"""
        user_id = update.effective_user.id
        
        # Check prerequisites first
        if not await self._check_prerequisites():
            await update.message.reply_text(
                "❌ **Không có credentials**\n\n"
                "Vui lòng thêm credentials tr<PERSON>ớc với `/addcreds`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        # Clear any existing wizard state
        self._clear_wizard(user_id)
        
        # Start createbot wizard
        self._set_wizard_step(user_id, 'createbot_credentials')
        
        # Get available credentials
        try:
            profiles = list_profiles()
            
            if len(profiles) == 1:
                # Only one profile, auto-select it
                profile = profiles[0]
                self._set_session_data(user_id, 'createbot_profile', profile['profile'])
                self._set_wizard_step(user_id, 'createbot_symbol')
                
                await update.message.reply_text(
                    f"🔑 **Sử dụng profile:** {profile['profile']}\n\n"
                    "Nhập symbol để trade (ví dụ: BTC, ETH, HYPER):",
                    reply_markup=ForceReply(selective=True),
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                # Multiple profiles, let user choose
                keyboard = []
                for profile in profiles:
                    display_name = profile.get('display_name', profile['profile'])
                    keyboard.append([InlineKeyboardButton(
                        f"🔑 {display_name}",
                        callback_data=f"createbot_profile_{profile['profile']}"
                    )])
                
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    "🔑 **Chọn tài khoản để sử dụng:**",
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.MARKDOWN
                )
                
        except Exception as e:
            self.logger.error(f"Error in createbot wizard: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_createbot_wizard_step(self, update: Update, context) -> None:
        """Handle steps in createbot wizard"""
        user_id = update.effective_user.id
        step = self._get_wizard_step(user_id)
        text = update.message.text.strip()
        
        if step == 'createbot_symbol':
            # Validate symbol
            if not self._validate_symbol(text):
                await self._send_error_message(update, "Symbol không hợp lệ")
                return
            
            # Normalize and save symbol
            normalized_symbol = self._normalize_symbol(text)
            self._set_session_data(user_id, 'createbot_symbol', normalized_symbol)
            self._set_wizard_step(user_id, 'createbot_amount')
            
            await update.message.reply_text(
                f"✅ Symbol: `{normalized_symbol}`\n\n"
                "Nhập số tiền để trade (USD):",
                reply_markup=ForceReply(selective=True),
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif step == 'createbot_amount':
            # Validate amount
            if not self._validate_amount(text):
                await self._send_error_message(update, "Số tiền không hợp lệ")
                return
            
            # Save amount and move to direction selection
            self._set_session_data(user_id, 'createbot_amount', text)
            self._set_wizard_step(user_id, 'createbot_direction')
            
            # Create direction selection keyboard
            keyboard = [
                [
                    InlineKeyboardButton("📈 LONG", callback_data="createbot_direction_LONG"),
                    InlineKeyboardButton("📉 SHORT", callback_data="createbot_direction_SHORT")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                f"✅ Amount: `${text}`\n\n"
                "Chọn hướng trade:",
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif step == 'createbot_test_mode':
            # Handle test mode selection
            test_mode = text.lower() in ['yes', 'y', 'test', 'true', '1']
            self._set_session_data(user_id, 'createbot_test_mode', test_mode)
            
            # Show confirmation
            await self._show_createbot_confirmation(update, user_id)
    
    async def handle_createbot_profile_callback(self, query, profile: str) -> None:
        """Handle profile selection in createbot wizard"""
        user_id = query.from_user.id
        
        # Save selected profile
        self._set_session_data(user_id, 'createbot_profile', profile)
        self._set_wizard_step(user_id, 'createbot_symbol')
        
        await query.edit_message_text(
            f"✅ **Đã chọn profile:** {profile}\n\n"
            "Nhập symbol để trade (ví dụ: BTC, ETH, HYPER):",
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Send follow-up message with ForceReply
        await query.message.reply_text(
            "Symbol:",
            reply_markup=ForceReply(selective=True)
        )
    
    async def handle_createbot_direction_callback(self, query, direction: str) -> None:
        """Handle direction selection in createbot wizard"""
        user_id = query.from_user.id
        
        # Save direction and move to test mode
        self._set_session_data(user_id, 'createbot_direction', direction)
        self._set_wizard_step(user_id, 'createbot_test_mode')
        
        # Create test mode selection keyboard
        keyboard = [
            [
                InlineKeyboardButton("🧪 Test Mode", callback_data="createbot_testmode_true"),
                InlineKeyboardButton("💰 Live Mode", callback_data="createbot_testmode_false")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            f"✅ **Hướng trade:** {direction}\n\n"
            "Chọn chế độ:",
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_createbot_testmode_callback(self, query, test_mode: str) -> None:
        """Handle test mode selection in createbot wizard"""
        user_id = query.from_user.id
        
        # Save test mode
        is_test = test_mode == 'true'
        self._set_session_data(user_id, 'createbot_test_mode', is_test)
        
        # Show confirmation
        await self._show_createbot_confirmation_callback(query, user_id)
    
    async def _show_createbot_confirmation(self, update: Update, user_id: int) -> None:
        """Show createbot confirmation"""
        # Get all saved data
        profile = self._get_session_data(user_id, 'createbot_profile')
        symbol = self._get_session_data(user_id, 'createbot_symbol')
        amount = self._get_session_data(user_id, 'createbot_amount')
        direction = self._get_session_data(user_id, 'createbot_direction', 'LONG')
        test_mode = self._get_session_data(user_id, 'createbot_test_mode', False)
        
        # Create confirmation message
        mode_text = "🧪 Test Mode" if test_mode else "💰 Live Mode"
        message = (
            "🤖 **Xác nhận tạo bot**\n\n"
            f"🔑 **Profile:** {profile}\n"
            f"📊 **Symbol:** {symbol}\n"
            f"💰 **Amount:** ${amount}\n"
            f"📈 **Direction:** {direction}\n"
            f"⚙️ **Mode:** {mode_text}\n\n"
            "Xác nhận tạo bot?"
        )
        
        # Create confirmation keyboard
        keyboard = [
            [
                InlineKeyboardButton("✅ Tạo Bot", callback_data="createbot_confirm"),
                InlineKeyboardButton("❌ Hủy", callback_data="createbot_cancel")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            message,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def _show_createbot_confirmation_callback(self, query, user_id: int) -> None:
        """Show createbot confirmation via callback"""
        # Get all saved data
        profile = self._get_session_data(user_id, 'createbot_profile')
        symbol = self._get_session_data(user_id, 'createbot_symbol')
        amount = self._get_session_data(user_id, 'createbot_amount')
        direction = self._get_session_data(user_id, 'createbot_direction', 'LONG')
        test_mode = self._get_session_data(user_id, 'createbot_test_mode', False)
        
        # Create confirmation message
        mode_text = "🧪 Test Mode" if test_mode else "💰 Live Mode"
        message = (
            "🤖 **Xác nhận tạo bot**\n\n"
            f"🔑 **Profile:** {profile}\n"
            f"📊 **Symbol:** {symbol}\n"
            f"💰 **Amount:** ${amount}\n"
            f"📈 **Direction:** {direction}\n"
            f"⚙️ **Mode:** {mode_text}\n\n"
            "Xác nhận tạo bot?"
        )
        
        # Create confirmation keyboard
        keyboard = [
            [
                InlineKeyboardButton("✅ Tạo Bot", callback_data="createbot_confirm"),
                InlineKeyboardButton("❌ Hủy", callback_data="createbot_cancel")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            message,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_createbot_confirm(self, query) -> None:
        """Handle createbot confirmation"""
        user_id = query.from_user.id
        
        try:
            # Get all saved data
            profile = self._get_session_data(user_id, 'createbot_profile')
            symbol = self._get_session_data(user_id, 'createbot_symbol')
            amount = self._get_session_data(user_id, 'createbot_amount')
            direction = self._get_session_data(user_id, 'createbot_direction', 'LONG')
            test_mode = self._get_session_data(user_id, 'createbot_test_mode', False)
            
            # Build command
            cmd_args = [
                self.bot_script_path, "start", symbol,
                "--amount", str(amount),
                "--profile", profile,
                "--direction", direction
            ]
            
            if test_mode:
                cmd_args.append("--test")
            
            # Execute command
            result = await self._execute_botsh_command(cmd_args)
            
            if result[0] == 0:
                mode_text = "🧪 Test Mode" if test_mode else "💰 Live Mode"
                await query.edit_message_text(
                    f"🚀 **Bot đã được tạo thành công!**\n\n"
                    f"📊 **Symbol:** {symbol}\n"
                    f"💰 **Amount:** ${amount}\n"
                    f"📈 **Direction:** {direction}\n"
                    f"⚙️ **Mode:** {mode_text}\n\n"
                    f"Sử dụng `/status {symbol}` để xem trạng thái.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Lỗi tạo bot:**\n```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            
            # Clear wizard state
            self._clear_wizard(user_id)
            
        except Exception as e:
            self.logger.error(f"Error creating bot: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )
            self._clear_wizard(user_id)
    
    async def handle_cancel(self, update: Update, context) -> None:
        """Handle /cancel command"""
        user_id = update.effective_user.id
        
        if self._is_in_wizard(user_id):
            self._clear_wizard(user_id)
            await self._send_success_message(update, "Đã hủy thao tác hiện tại")
        else:
            await self._send_info_message(update, "Không có thao tác nào để hủy")
