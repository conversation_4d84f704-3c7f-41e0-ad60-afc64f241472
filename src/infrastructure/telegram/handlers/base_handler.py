#!/usr/bin/env python3
"""Base handler for Telegram bot operations."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

from ..telegram_base import TelegramBaseHandler, UserSessionManager, ValidationUtils
from ..templates import TelegramTemplates
from ..trading_notifications import TradingNotificationManager

# Import centralized utilities
from ....core.constants import CREDENTIALS_DIR, ensure_directories
from ....core.credential_utils import list_profiles, load_credentials, store_credentials


class BaseTelegramHandler(TelegramBaseHandler):
    """Base class for all Telegram handlers with common functionality."""

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass
    
    def __init__(self, bot_token: str, chat_id: int):
        super().__init__('BaseTelegramHandler')
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.application = None
        self.session_manager = UserSessionManager()
        self.bot_script_path = "/app/bot.sh"  # Path to bot.sh script

        # Initialize notification manager
        self.notification_manager = TradingNotificationManager(bot_token, chat_id)
    
    async def _execute_botsh_command(self, command: List[str]) -> Tuple[int, str, str]:
        """Execute bot.sh command and return (exit_code, stdout, stderr)"""
        try:
            # Run command asynchronously
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/app"
            )
            
            stdout, stderr = await process.communicate()
            
            return (
                process.returncode or 0,
                stdout.decode('utf-8', errors='ignore').strip(),
                stderr.decode('utf-8', errors='ignore').strip()
            )
            
        except Exception as e:
            self.logger.error(f"Error executing command {' '.join(command)}: {e}")
            return 1, "", str(e)
    
    def _parse_credentials_list(self, output: str) -> List[Dict[str, str]]:
        """Parse credentials list from bot.sh output"""
        credentials = []
        lines = output.split('\n')
        
        current_profile = None
        current_display_name = None
        
        for line in lines:
            line = line.strip()
            
            # New format: 🔑 profile_name
            if line.startswith('🔑 '):
                current_profile = line[2:].strip()
                current_display_name = current_profile  # Default to profile name
                
            # Extract display name: Display Name: actual_name
            elif line.startswith('Display Name:') and current_profile:
                current_display_name = line.split(':', 1)[1].strip()
                
            # When we hit Format: or Total:, save the current credential
            elif (line.startswith('Format:') or line.startswith('📊 Total:')) and current_profile:
                credentials.append({
                    'profile': current_profile,
                    'display_name': current_display_name or current_profile
                })
                current_profile = None
                current_display_name = None
        
        # Handle case where last credential doesn't have Format line
        if current_profile:
            credentials.append({
                'profile': current_profile,
                'display_name': current_display_name or current_profile
            })

        return credentials
    
    async def _check_prerequisites(self) -> bool:
        """Check if prerequisites (credentials) are available"""
        try:
            # Use centralized credential utilities
            profiles = list_profiles()
            return len(profiles) > 0
            
        except Exception as e:
            self.logger.error(f"Prerequisites check failed with exception: {e}")
            return False
    
    def _create_inline_keyboard(self, buttons: List[List[Dict[str, str]]]) -> InlineKeyboardMarkup:
        """Create inline keyboard from button configuration"""
        keyboard = []
        for row in buttons:
            keyboard_row = []
            for button in row:
                keyboard_row.append(InlineKeyboardButton(
                    text=button['text'],
                    callback_data=button['callback_data']
                ))
            keyboard.append(keyboard_row)
        return InlineKeyboardMarkup(keyboard)
    
    async def _send_error_message(self, update: Update, error_msg: str):
        """Send formatted error message"""
        await update.message.reply_text(
            f"❌ **Lỗi:** {error_msg}",
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def _send_success_message(self, update: Update, success_msg: str):
        """Send formatted success message"""
        await update.message.reply_text(
            f"✅ **Thành công:** {success_msg}",
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def _send_info_message(self, update: Update, info_msg: str):
        """Send formatted info message"""
        await update.message.reply_text(
            f"ℹ️ **Thông tin:** {info_msg}",
            parse_mode=ParseMode.MARKDOWN
        )
    
    def _validate_symbol(self, symbol: str) -> bool:
        """Validate trading symbol"""
        return ValidationUtils.validate_symbol(symbol)
    
    def _validate_amount(self, amount: str) -> bool:
        """Validate trading amount"""
        return ValidationUtils.validate_amount(amount)
    
    def _validate_api_key(self, api_key: str) -> bool:
        """Validate API key"""
        return ValidationUtils.validate_api_key(api_key)
    
    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol to full format"""
        if "/" not in symbol:
            # Simple symbol like "btc" -> "BTC/USDT:USDT"
            return f"{symbol.upper()}/USDT:USDT"
        return symbol
    
    def _get_container_name(self, symbol: str) -> str:
        """Generate container name from symbol"""
        return symbol.lower().replace('/', '').replace(':', '').replace('-', '').replace('_', '')
    
    async def _get_active_containers(self) -> List[Dict[str, Any]]:
        """Get list of active trading containers"""
        try:
            result = await self._execute_botsh_command([self.bot_script_path, "list"])
            if result[0] == 0:
                # Parse container list from output
                containers = []
                lines = result[1].split('\n')
                for line in lines:
                    if '🤖' in line and 'Running' in line:
                        # Extract container info
                        parts = line.split()
                        if len(parts) >= 2:
                            containers.append({
                                'name': parts[1],
                                'status': 'Running',
                                'symbol': parts[1].upper()
                            })
                return containers
            return []
        except Exception as e:
            self.logger.error(f"Error getting active containers: {e}")
            return []
    
    async def _format_container_status(self, containers: List[Dict[str, Any]]) -> str:
        """Format container status for display"""
        if not containers:
            return "📭 **Không có bot nào đang chạy**"
        
        message = "🤖 **Bot đang hoạt động:**\n\n"
        for container in containers:
            message += f"• **{container['symbol']}** - {container['status']}\n"
        
        return message
    
    def _get_session_data(self, user_id: int, key: str, default=None):
        """Get data from user session"""
        return self.session_manager.get_session_data(user_id, key, default)
    
    def _set_session_data(self, user_id: int, key: str, value):
        """Set data in user session"""
        self.session_manager.set_session_data(user_id, key, value)
    
    def _clear_session(self, user_id: int):
        """Clear user session"""
        self.session_manager.clear_session(user_id)
    
    def _is_in_wizard(self, user_id: int) -> bool:
        """Check if user is in a wizard"""
        return self.session_manager.is_in_wizard(user_id)
    
    def _get_wizard_step(self, user_id: int) -> Optional[str]:
        """Get current wizard step"""
        return self.session_manager.get_wizard_step(user_id)
    
    def _set_wizard_step(self, user_id: int, step: str):
        """Set wizard step"""
        self.session_manager.set_wizard_step(user_id, step)
    
    def _clear_wizard(self, user_id: int):
        """Clear wizard state"""
        self.session_manager.clear_wizard(user_id)
