"""Telegram Credentials Manager - Handle API credentials via Telegram"""
from typing import Dict, List, Optional, Any
from .telegram_base import Tel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ValidationUtils, MessageFormatter

try:
    from telegram.constants import ParseMode
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False


class TelegramCredentialsManager(TelegramBaseHandler):
    """Manages API credentials through Telegram interface"""
    
    def __init__(self, session_manager):
        super().__init__('TelegramCredentialsManager')
        self.session_manager = session_manager
    
    # ===============================
    # Basic Credential Commands
    # ===============================
    
    async def handle_setkey_command(self, update, context):
        """Handle /setkey command - Quick setup for default profile"""
        try:
            args = context.args
            
            if len(args) != 2:
                await self.send_message(
                    context.bot, 
                    update.effective_chat.id,
                    "🔑 <b>Quick Setup API Credentials</b>\n\n"
                    "Usage: `/setkey <api_key> <api_secret>`\n\n"
                    "Example:\n"
                    "`/setkey your_api_key_here your_secret_here`\n\n"
                    "🔐 Will be stored as 'default' profile\n"
                    "💡 For advanced setup with custom name: `/addcreds`"
                )
                return
            
            api_key, api_secret = args
            
            # Validate inputs
            if not ValidationUtils.validate_api_key(api_key):
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "❌ Invalid API key format. Must be at least 10 characters."
                )
                return
            
            # Store credentials
            command = ["./bot.sh", "store-credentials", "default", api_key, api_secret, "Default Account"]
            response = await self.execute_botsh_with_response(command)
            
            await self.send_message(context.bot, update.effective_chat.id, response)
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "setkey")
            )
    
    async def handle_listcreds_command(self, update, context):
        """Handle /listcreds command - List all credential profiles"""
        try:
            command = ["./bot.sh", "list-credentials"]
            response = await self.execute_botsh_with_response(command)
            
            # Create management keyboard
            keyboard = self.create_keyboard([
                [("➕ Add New", "creds_add"), ("🔧 Manage", "creds_manage")],
                [("🔄 Refresh", "creds_list"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                response,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "listcreds")
            )
    
    async def handle_showcreds_command(self, update, context):
        """Handle /showcreds command - Show specific credential details"""
        try:
            profile = context.args[0] if context.args else "default"
            
            command = ["python3", "src/cli/credentials_cli.py", "show", profile]
            response = await self.execute_botsh_with_response(command)
            
            # Create management keyboard for this profile
            keyboard = self.create_keyboard([
                [("✏️ Edit", f"creds_edit_{profile}"), ("🗑️ Delete", f"creds_delete_{profile}")],
                [("📋 List All", "creds_list"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                response,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "showcreds")
            )
    
    async def handle_deletecreds_command(self, update, context):
        """Handle /deletecreds command - Delete credential profile"""
        try:
            if not context.args:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "🗑️ <b>Delete Credentials</b>\n\n"
                    "Usage: <code>/deletecreds &lt;profile&gt;</code>\n\n"
                    "Example: `/deletecreds testnet`\n\n"
                    "⚠️ This cannot be undone!"
                )
                return
            
            profile = context.args[0]
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("✅ Yes, Delete", f"creds_confirm_delete_{profile}"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"⚠️ <b>Confirm Deletion</b>\n\n"
                f"Are you sure you want to delete credentials for profile: `{profile}`?\n\n"
                f"This action cannot be undone!",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "deletecreds")
            )
    
    # ===============================
    # Advanced Credential Wizard
    # ===============================
    
    async def handle_addcreds_wizard(self, update, context):
        """Handle /addcreds command - Start credential wizard"""
        try:
            user_id = update.effective_user.id
            
            # Clear any existing wizard
            self.session_manager.clear_session(user_id)
            
            # Start wizard
            self.session_manager.start_wizard(user_id, 'add_credentials')
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                "🔑 <b>Add New Credentials Wizard</b>\n\n"
                "Step 1/4: What would you like to name this credential profile?\n\n"
                "Examples: `main`, `testnet`, `account2`\n\n"
                "🔤 Enter profile name (or /cancel to abort):"
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "addcreds")
            )
    
    async def handle_selectcreds_command(self, update, context):
        """Handle /selectcreds command - Select credentials for operations"""
        try:
            # Get list of available credentials
            returncode, stdout, stderr = await self.execute_botsh_command(["./bot.sh", "list-credentials"])
            
            if returncode != 0:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "❌ No credentials found. Please add credentials first with `/addcreds`"
                )
                return
            
            # Parse credentials from output (simplified)
            profiles = []
            for line in stdout.split('\n'):
                if line.startswith('• '):
                    profile_info = line[2:].strip()
                    if '(' in profile_info:
                        profile_id = profile_info.split('(')[0].strip()
                        profiles.append(profile_id)
            
            if not profiles:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "📭 No credential profiles found.\n\n"
                    "💡 Use `/addcreds` to add API credentials"
                )
                return
            
            # Create selection keyboard
            keyboard_buttons = []
            for profile in profiles[:8]:  # Limit to 8 profiles for keyboard size
                keyboard_buttons.append([(f"🔑 {profile}", f"creds_select_{profile}")])
            
            keyboard_buttons.append([("❌ Cancel", "close")])
            keyboard = self.create_keyboard(keyboard_buttons)
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                "🔑 <b>Select Credentials</b>\n\n"
                "Choose a credential profile to use:",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "selectcreds")
            )
    
    # ===============================
    # Wizard Text Input Handler
    # ===============================
    
    async def handle_wizard_input(self, update, context) -> bool:
        """Handle text input for credential wizards. Returns True if handled."""
        user_id = update.effective_user.id
        
        if not self.session_manager.is_wizard_active(user_id):
            return False
        
        session = self.session_manager.get_session(user_id)
        wizard_state = session.get('wizard_state')
        
        if wizard_state != 'add_credentials':
            return False
        
        return await self._handle_add_credentials_input(update, context)
    
    async def _handle_add_credentials_input(self, update, context) -> bool:
        """Handle input for add credentials wizard"""
        user_id = update.effective_user.id
        text = update.message.text.strip()
        wizard_data = self.session_manager.get_wizard_data(user_id)
        
        try:
            # Step 1: Profile name
            if 'profile_name' not in wizard_data:
                if not ValidationUtils.validate_profile_name(text):
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ Invalid profile name. Use only letters, numbers, hyphens, and underscores.\n\n"
                        "Please enter a valid profile name:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'profile_name', text)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Profile name: `{text}`\n\n"
                    "Step 2/4: Enter your API Key\n\n"
                    "🔐 Paste your API key:"
                )
                return True
            
            # Step 2: API Key
            elif 'api_key' not in wizard_data:
                if not ValidationUtils.validate_api_key(text):
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ Invalid API key format. Must be at least 10 characters.\n\n"
                        "Please enter a valid API key:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'api_key', text)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "✅ API Key saved\n\n"
                    "Step 3/4: Enter your API Secret\n\n"
                    "🔑 Paste your API secret:"
                )
                return True
            
            # Step 3: API Secret
            elif 'api_secret' not in wizard_data:
                if len(text) < 10:
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ API secret too short. Must be at least 10 characters.\n\n"
                        "Please enter your API secret:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'api_secret', text)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "✅ API Secret saved\n\n"
                    "Step 4/4: Enter a display name (optional)\n\n"
                    "📝 Enter a friendly name for this account\n"
                    "(or send 'skip' to use profile name):"
                )
                return True
            
            # Step 4: Display name
            elif 'display_name' not in wizard_data:
                display_name = text if text.lower() != 'skip' else wizard_data['profile_name']
                self.session_manager.update_wizard_data(user_id, 'display_name', display_name)
                
                # Final confirmation
                profile_name = wizard_data['profile_name']
                api_key = wizard_data['api_key']
                
                keyboard = self.create_keyboard([
                    [("✅ Save Credentials", "creds_save_confirm"), ("❌ Cancel", "creds_cancel")]
                ])
                
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"📋 <b>Credential Summary</b>\n\n"
                    f"Profile: `{profile_name}`\n"
                    f"Display Name: `{display_name}`\n"
                    f"API Key: `{api_key[:10]}...{api_key[-4:]}`\n"
                    f"API Secret: `*<b>{api_key[-4:]}`\n\n"
                    f"Ready to save?",
                    reply_markup=keyboard
                )
                return True
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "wizard input")
            )
            self.session_manager.finish_wizard(user_id)
        
        return True
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for credentials. Returns True if handled."""
        if not data.startswith('creds_'):
            return False
        
        try:
            if data == "creds_add":
                await self._handle_callback_add(query)
            elif data == "creds_list":
                await self._handle_callback_list(query)
            elif data == "creds_manage":
                await self._handle_callback_manage(query)
            elif data == "creds_save_confirm":
                await self._handle_callback_save_confirm(query)
            elif data == "creds_cancel":
                await self._handle_callback_cancel(query)
            elif data.startswith("creds_select_"):
                profile = data.replace("creds_select_", "")
                await self._handle_callback_select(query, profile)
            elif data.startswith("creds_edit_"):
                profile = data.replace("creds_edit_", "")
                await self._handle_callback_edit(query, profile)
            elif data.startswith("creds_delete_"):
                profile = data.replace("creds_delete_", "")
                await self._handle_callback_delete(query, profile)
            elif data.startswith("creds_confirm_delete_"):
                profile = data.replace("creds_confirm_delete_", "")
                await self._handle_callback_confirm_delete(query, profile)
            else:
                return False
            
            return True
            
        except Exception as e:
            await query.answer(f"Error: {str(e)}")
            return True
    
    async def _handle_callback_add(self, query):
        """Handle add credentials callback"""
        user_id = query.from_user.id
        self.session_manager.clear_session(user_id)
        self.session_manager.start_wizard(user_id, 'add_credentials')
        
        await self.edit_message(
            query,
            "🔑 </b>Add New Credentials Wizard<b>\n\n"
            "Step 1/4: What would you like to name this credential profile?\n\n"
            "Examples: `main`, `testnet`, `account2`\n\n"
            "🔤 Enter profile name (or /cancel to abort):"
        )
    
    async def _handle_callback_list(self, query):
        """Handle list credentials callback"""
        command = ["./bot.sh", "list-credentials"]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("➕ Add New", "creds_add"), ("🔧 Manage", "creds_manage")],
            [("🔄 Refresh", "creds_list"), ("❌ Close", "close")]
        ])
        
        await self.edit_message(query, response, reply_markup=keyboard)
    
    async def _handle_callback_manage(self, query):
        """Handle manage credentials callback"""
        await self.edit_message(
            query,
            "🔧 </b>Credential Management<b>\n\n"
            "Available actions:\n"
            "• View specific profile: <code>/showcreds &lt;profile&gt;</code>\n"
"• Delete profile: <code>/deletecreds &lt;profile&gt;</code>\n"
            "• Add new profile: `/addcreds`\n"
            "• Select for use: `/selectcreds`\n\n"
            "💡 Use commands above for detailed management."
        )
    
    async def _handle_callback_save_confirm(self, query):
        """Handle save credentials confirmation"""
        user_id = query.from_user.id
        wizard_data = self.session_manager.get_wizard_data(user_id)
        
        if not wizard_data:
            await query.answer("Session expired. Please start again.")
            return
        
        # Execute save command
        command = [
            "./bot.sh", "store-credentials",
            wizard_data['profile_name'],
            wizard_data['api_key'],
            wizard_data['api_secret'],
            wizard_data['display_name']
        ]
        
        response = await self.execute_botsh_with_response(command)
        
        # Finish wizard
        self.session_manager.finish_wizard(user_id)
        
        keyboard = self.create_keyboard([
            [("📋 List All", "creds_list"), ("➕ Add Another", "creds_add")],
            [("❌ Close", "close")]
        ])
        
        await self.edit_message(
            query,
            f"{response}\n\n💡 Credentials are now ready to use!",
            reply_markup=keyboard
        )
    
    async def _handle_callback_cancel(self, query):
        """Handle cancel wizard callback"""
        user_id = query.from_user.id
        self.session_manager.finish_wizard(user_id)
        
        await self.edit_message(query, "❌ </b>Operation Cancelled<b>\n\nWizard stopped.")
    
    async def _handle_callback_select(self, query, profile: str):
        """Handle select credentials callback"""
        command = ["./bot.sh", "load-credentials", profile]
        response = await self.execute_botsh_with_response(command)
        
        await self.edit_message(
            query,
            f"🔑 </b>Credentials Selected<b>\n\n"
            f"Profile: `{profile}`\n\n"
            f"{response}\n\n"
            f"✅ Ready to use for bot operations!"
        )
    
    async def _handle_callback_edit(self, query, profile: str):
        """Handle edit credentials callback"""
        await self.edit_message(
            query,
            f"✏️ </b>Edit Credentials: {profile}<b>\n\n"
            f"Current editing options:\n"
            f"• Delete and recreate: `/deletecreds {profile}` then `/addcreds`\n"
            f"• View details: `/showcreds {profile}`\n\n"
            f"💡 Direct editing coming in next update!"
        )
    
    async def _handle_callback_delete(self, query, profile: str):
        """Handle delete credentials callback"""
        keyboard = self.create_keyboard([
            [("✅ Yes, Delete", f"creds_confirm_delete_{profile}"), ("❌ Cancel", "close")]
        ])
        
        await self.edit_message(
            query,
            f"⚠️ </b>Confirm Deletion**\n\n"
            f"Are you sure you want to delete credentials for profile: `{profile}`?\n\n"
            f"This action cannot be undone!",
            reply_markup=keyboard
        )
    
    async def _handle_callback_confirm_delete(self, query, profile: str):
        """Handle confirm delete credentials callback"""
        command = ["python3", "src/cli/credentials_cli.py", "delete", profile, "--force"]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("📋 List Remaining", "creds_list"), ("➕ Add New", "creds_add")],
            [("❌ Close", "close")]
        ])
        
        await self.edit_message(
            query,
            f"{response}\n\n🗑️ Profile deleted successfully.",
            reply_markup=keyboard
        )
    
    # ===============================
    # Abstract Method Implementation
    # ===============================
    
    async def handle_command(self, update, context) -> None:
        """Main command dispatcher"""
        # This is handled by individual command handlers
        pass 