#!/usr/bin/env python3
"""
Bot Creator CLI - Handle complex bot creation logic
Replaces complex bot creation from bot.sh
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))


class BotCreator:
    """Handle trading bot creation"""
    
    def __init__(self):
        self.docker_cmd = "docker"
        self.trader_image = os.getenv('TRADER_IMAGE', 'autotrader-trader:latest')
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate trading symbol"""
        if not symbol:
            return False
        
        # Allow simple symbols (eth, btc) and complex ones (ETH/USDT:USDT)
        if len(symbol) < 2 or len(symbol) > 20:
            return False
        
        # Basic character validation
        allowed_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789/:')
        return all(c in allowed_chars for c in symbol)
    
    def validate_amount(self, amount: str) -> bool:
        """Validate trading amount"""
        try:
            value = float(amount)
            return value > 0
        except ValueError:
            return False
    
    def normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol to container name"""
        if "/" in symbol:
            # Full format like "ETH/USDT:USDT" -> "eth"
            base_symbol = symbol.split("/")[0]
        elif symbol.endswith("USDT") and len(symbol) > 4:
            # Format like "ETHUSDT" -> "eth"
            base_symbol = symbol[:-4]
        else:
            # Simple format like "ETH" or "eth" -> "eth"
            base_symbol = symbol
        
        return base_symbol.lower()
    
    def check_container_exists(self, container_name: str) -> bool:
        """Check if container already exists"""
        try:
            result = subprocess.run([
                self.docker_cmd, "ps", "-a", "--format", "{{.Names}}"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                containers = result.stdout.strip().split('\n')
                return container_name in containers
            
            return False
        except Exception:
            return False
    
    def create_trading_bot(self, symbol: str, amount: float, **options) -> int:
        """Create and start a trading bot"""
        try:
            # Validate inputs
            if not self.validate_symbol(symbol):
                print(f"❌ Invalid symbol: {symbol}")
                return 1
            
            if amount <= 0:
                print(f"❌ Invalid amount: {amount}")
                return 1
            
            # Generate container name
            container_name = self.normalize_symbol(symbol)
            
            # Check if container already exists
            if self.check_container_exists(container_name):
                print(f"❌ Container '{container_name}' already exists")
                print(f"💡 Use 'python3 src/cli/autotrader_cli.py restart {symbol}' to restart it")
                return 1
            
            # Check credentials
            api_key = os.getenv('BYBIT_API_KEY')
            api_secret = os.getenv('BYBIT_API_SECRET')
            
            if not api_key or not api_secret:
                print("❌ Missing API credentials")
                print("💡 Set environment variables:")
                print("   export BYBIT_API_KEY='your_key'")
                print("   export BYBIT_API_SECRET='your_secret'")
                print("💡 Or use: python3 src/cli/credentials_cli.py load <profile> --export")
                return 1
            
            # Prepare Docker command
            docker_args = [
                self.docker_cmd, "run", "-d",
                "--name", container_name,
                "--restart", "unless-stopped",
                "-e", f"BYBIT_API_KEY={api_key}",
                "-e", f"BYBIT_API_SECRET={api_secret}",
                "-e", f"SYMBOL={symbol}",
                "-e", f"AMOUNT={amount}",
            ]
            
            # Add optional parameters
            if options.get('test_mode'):
                docker_args.extend(["-e", "TEST_MODE=true"])
            
            if options.get('direction'):
                docker_args.extend(["-e", f"DIRECTION={options['direction']}"])
            
            if options.get('stop_loss'):
                docker_args.extend(["-e", f"STOP_LOSS={options['stop_loss']}"])
            
            if options.get('take_profit'):
                docker_args.extend(["-e", f"TAKE_PROFIT={options['take_profit']}"])
            
            # Add volume mounts
            docker_args.extend([
                "-v", f"{Path.cwd()}/data:/app/data",
                "-v", f"{Path.cwd()}/logs:/app/logs",
                "-v", f"{Path.cwd()}/configs:/app/configs",
            ])
            
            # Add image
            docker_args.append(self.trader_image)
            
            print(f"🤖 Creating trading bot: {symbol}")
            print(f"💰 Amount: {amount} USDT")
            print(f"🏷️ Container: {container_name}")
            
            # Run Docker command
            result = subprocess.run(docker_args, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                container_id = result.stdout.strip()
                print(f"✅ Trading bot created successfully!")
                print(f"🆔 Container ID: {container_id[:12]}")
                print(f"📊 Monitor with: python3 src/cli/autotrader_cli.py status {symbol}")
                print(f"📋 View logs with: python3 src/cli/autotrader_cli.py logs {symbol}")
                return 0
            else:
                print(f"❌ Failed to create trading bot")
                print(f"Error: {result.stderr}")
                return 1
                
        except Exception as e:
            print(f"❌ Error creating trading bot: {e}")
            return 1
    
    def parse_options(self, args: List[str]) -> Dict:
        """Parse command line options"""
        options = {}
        i = 0
        
        while i < len(args):
            arg = args[i]
            
            if arg == '--test-mode':
                options['test_mode'] = True
            elif arg == '--direction' and i + 1 < len(args):
                options['direction'] = args[i + 1]
                i += 1
            elif arg == '--stop-loss' and i + 1 < len(args):
                try:
                    options['stop_loss'] = float(args[i + 1])
                    i += 1
                except ValueError:
                    print(f"❌ Invalid stop-loss value: {args[i + 1]}")
            elif arg == '--take-profit' and i + 1 < len(args):
                try:
                    options['take_profit'] = float(args[i + 1])
                    i += 1
                except ValueError:
                    print(f"❌ Invalid take-profit value: {args[i + 1]}")
            elif arg.startswith('--'):
                print(f"⚠️ Unknown option: {arg}")
            
            i += 1
        
        return options


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="AutoTrader Bot Creator",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('symbol', help='Trading symbol (e.g., eth, btc, ETH/USDT)')
    parser.add_argument('--amount', type=float, default=50.0,
                       help='Trading amount in USDT (default: 50)')
    parser.add_argument('--test-mode', action='store_true',
                       help='Enable test mode')
    parser.add_argument('--direction', choices=['long', 'short', 'both'],
                       default='long', help='Trading direction (default: long)')
    parser.add_argument('--stop-loss', type=float,
                       help='Stop loss percentage')
    parser.add_argument('--take-profit', type=float,
                       help='Take profit percentage')
    
    args = parser.parse_args()
    
    creator = BotCreator()
    
    # Prepare options
    options = {
        'test_mode': args.test_mode,
        'direction': args.direction,
    }
    
    if args.stop_loss:
        options['stop_loss'] = args.stop_loss
    
    if args.take_profit:
        options['take_profit'] = args.take_profit
    
    return creator.create_trading_bot(args.symbol, args.amount, **options)


if __name__ == '__main__':
    sys.exit(main())
