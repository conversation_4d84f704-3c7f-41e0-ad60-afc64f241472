#!/usr/bin/env python3
"""
Credentials CLI - Handle credential operations
Replaces complex credential logic from bot.sh
"""

import sys
import os
import json
import getpass
from pathlib import Path
from typing import Dict, List, Optional
import argparse

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.constants import ensure_directories
from core.credential_utils import (
    credential_manager,
    store_credentials,
    load_credentials,
    list_profiles,
    delete_profile,
    set_environment_variables,
    get_export_commands
)


class CredentialsCLI:
    """Handle credential operations using centralized utilities"""

    def __init__(self):
        # Ensure directories exist
        ensure_directories()

    def _parse_env_file(self, env_file: Path) -> Dict:
        """Parse .env file into credential data"""
        try:
            content = env_file.read_text()
            data = {}

            for line in content.splitlines():
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                if '=' in line:
                    key, value = line.split('=', 1)
                    data[key.strip()] = value.strip()

            # Map to standard format
            result = {
                'profile': env_file.stem,
                'api_key': data.get('BYBIT_API_KEY', ''),
                'api_secret': data.get('BYBIT_API_SECRET', ''),
                'version': 'env'
            }

            # Try to get display name from .display file
            display_file = env_file.parent / f"{env_file.stem}.display"
            if display_file.exists():
                result['display_name'] = display_file.read_text().strip()
            else:
                result['display_name'] = env_file.stem

            return result
        except Exception as e:
            return {
                'profile': env_file.stem,
                'error': str(e),
                'version': 'env'
            }

    def list_credentials(self, show_details: bool = False) -> int:
        """List all credential profiles using centralized utility"""
        try:
            profiles = list_profiles()

            if not profiles:
                print("📭 No credential profiles found")
                print("")
                print("💡 Use '/addcreds' in Telegram bot to add credentials")
                return 0

            print("🔑 Stored Credential Profiles")
            print("=" * 50)

            for profile_info in profiles:
                profile = profile_info['profile']
                display_name = profile_info['display_name']
                format_type = profile_info.get('format', 'json')

                print(f"🔑 {profile}")
                print(f"   Display Name: {display_name}")
                print(f"   Format: {format_type}")
                if profile_info.get('error'):
                    print("   ⚠️ Error reading file")
                print("")

            print(f"📊 Total: {len(profiles)} profiles")
            return 0

        except Exception as e:
            print(f"❌ Error listing credentials: {e}")
            return 1


    def store_credentials(self, profile: str, api_key: str, api_secret: str,
                         display_name: str = None, interactive: bool = True) -> int:
        """Store credentials using centralized utility"""
        try:
            if not display_name:
                if interactive:
                    display_name = input(f"Enter display name for profile '{profile}' (default: {profile}): ").strip()
                    if not display_name:
                        display_name = profile
                else:
                    display_name = profile

            # Validate inputs
            if not all([profile, api_key, api_secret]):
                print("❌ Profile name, API key, and API secret are required")
                return 1

            if len(api_key) < 10:
                print("❌ API key seems too short (minimum 10 characters)")
                return 1

            if len(api_secret) < 10:
                print("❌ API secret seems too short (minimum 10 characters)")
                return 1

            # Check if profile exists
            existing_cred = load_credentials(profile)
            if existing_cred and interactive:
                response = input(f"Profile '{profile}' already exists. Overwrite? [y/N]: ")
                if response.lower() not in ['y', 'yes']:
                    print("❌ Operation cancelled")
                    return 1

            # Use centralized storage
            success = store_credentials(profile, api_key, api_secret, display_name, 'cli')

            if success:
                print(f"✅ Credentials stored successfully for profile: {profile}")
                print(f"🔑 API Key: {api_key[:10]}...{api_key[-4:]}")
                return 0
            else:
                print(f"❌ Failed to store credentials for profile: {profile}")
                return 1

        except Exception as e:
            print(f"❌ Error storing credentials: {e}")
            return 1

    def load_credentials(self, profile: str, export_env: bool = False) -> int:
        """Load credentials using centralized utility"""
        try:
            cred_data = load_credentials(profile)

            if not cred_data:
                print(f"❌ Profile '{profile}' not found")
                return 1

            api_key = cred_data.get('api_key', '')
            api_secret = cred_data.get('api_secret', '')

            if export_env:
                # Set environment variables
                success = set_environment_variables(profile)
                if success:
                    print(f"✅ Loaded credentials for profile: {profile}")
                    print("🔑 Environment variables set: BYBIT_API_KEY, BYBIT_API_SECRET")
                else:
                    print(f"❌ Failed to set environment variables for profile: {profile}")
                    return 1
            else:
                # Display export commands
                export_commands = get_export_commands(profile)
                if export_commands:
                    print(f"🔑 Export commands for profile: {profile}")
                    print("=" * 50)
                    print(export_commands[0])
                    print(export_commands[1])
                    print("")
                    print("💡 Copy and paste these commands to load credentials")
                else:
                    print(f"❌ Failed to generate export commands for profile: {profile}")
                    return 1

            return 0

        except Exception as e:
            print(f"❌ Error loading credentials: {e}")
            return 1

    def show_credentials(self, profile: str, show_secret: bool = False) -> int:
        """Show credentials using centralized utility"""
        try:
            cred_data = load_credentials(profile)

            if not cred_data:
                print(f"❌ Profile '{profile}' not found")
                print("")
                print("💡 Available profiles:")
                self.list_credentials()
                return 1

            print(f"🔐 Credential Details for Profile: {profile}")
            print("=" * 50)
            print(f"   Display Name: {cred_data['display_name']}")

            api_key = cred_data['api_key']
            api_secret = cred_data['api_secret']

            if show_secret:
                print(f"   API Key: {api_key}")
                print(f"   API Secret: {api_secret}")
            else:
                print(f"   API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else '***'}")
                print(f"   API Secret: {'*' * 20}")

            return 0

        except Exception as e:
            print(f"❌ Error showing credentials: {e}")
            return 1

    def delete_credentials(self, profile: str, force: bool = False) -> int:
        """Delete credentials using centralized utility"""
        try:
            cred_data = load_credentials(profile)

            if not cred_data:
                print(f"❌ Profile '{profile}' not found")
                return 1

            if not force:
                response = input(f"⚠️ Delete credentials for profile '{profile}'? [y/N]: ")
                if response.lower() not in ['y', 'yes']:
                    print("❌ Operation cancelled")
                    return 1

            success = delete_profile(profile)

            if success:
                print(f"✅ Credentials deleted for profile: {profile}")
                return 0
            else:
                print(f"❌ Failed to delete credentials for profile: {profile}")
                return 1

        except Exception as e:
            print(f"❌ Error deleting credentials: {e}")
            return 1


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="AutoTrader Credentials CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # List command
    list_parser = subparsers.add_parser('list', help='List credential profiles')
    list_parser.add_argument('--details', action='store_true',
                           help='Show detailed information')

    # Store command
    store_parser = subparsers.add_parser('store', help='Store credentials')
    store_parser.add_argument('profile', help='Profile name')
    store_parser.add_argument('--api-key', help='API Key')
    store_parser.add_argument('--api-secret', help='API Secret')
    store_parser.add_argument('--display-name', help='Display name')
    store_parser.add_argument('--non-interactive', action='store_true',
                            help='Non-interactive mode (requires all parameters)')

            print(f"🔑 Credentials for profile: {profile}")
            print("=" * 40)
            print(f"Display Name: {cred_data.get('display_name', profile)}")

            api_key = cred_data.get('api_key', '')
            if len(api_key) > 14:
                print(f"API Key: {api_key[:10]}...{api_key[-4:]}")
            else:
                print(f"API Key: {api_key}")

            if show_secret:
                print(f"API Secret: {cred_data.get('api_secret', '')}")
            else:
                print(f"API Secret: {'*' * 20}")

            print(f"Format: {cred_data.get('version', 'unknown')}")
            created = cred_data.get('created', 'Unknown')
            if created != 'Unknown':
                print(f"Created: {created}")

            return 0

        except Exception as e:
            print(f"❌ Error showing credentials: {e}")
            return 1
    
    def load_credentials(self, profile: str, export_env: bool = False) -> int:
        """Load credentials into environment or display export commands"""
        try:
            cred_file = self._find_credential_file(profile)

            if not cred_file:
                print(f"❌ Profile '{profile}' not found")
                return 1

            cred_data = self._load_credential_data(cred_file)

            api_key = cred_data.get('api_key', '')
            api_secret = cred_data.get('api_secret', '')

            if export_env:
                # Set environment variables
                os.environ['BYBIT_API_KEY'] = api_key
                os.environ['BYBIT_API_SECRET'] = api_secret
                print(f"✅ Loaded credentials for profile: {profile}")
                print("🔑 Environment variables set: BYBIT_API_KEY, BYBIT_API_SECRET")
            else:
                # Display export commands
                print(f"🔑 Export commands for profile: {profile}")
                print("=" * 50)
                print(f"export BYBIT_API_KEY='{api_key}'")
                print(f"export BYBIT_API_SECRET='{api_secret}'")
                print("")
                print("💡 Copy and paste these commands to load credentials")

            return 0

        except Exception as e:
            print(f"❌ Error loading credentials: {e}")
            return 1
    
    def delete_credentials(self, profile: str, force: bool = False) -> int:
        """Delete credentials for a profile"""
        try:
            cred_file = self._find_credential_file(profile)

            if not cred_file:
                print(f"❌ Profile '{profile}' not found")
                return 1

            if not force:
                response = input(f"⚠️ Delete credentials for profile '{profile}'? [y/N]: ")
                if response.lower() not in ['y', 'yes']:
                    print("❌ Operation cancelled")
                    return 1

            # Delete main credential file
            cred_file.unlink()

            # If it's an ENV file, also delete the display file
            if cred_file.suffix == '.env':
                display_file = cred_file.parent / f"{profile}.display"
                if display_file.exists():
                    display_file.unlink()

            print(f"✅ Credentials deleted for profile: {profile}")

            return 0

        except Exception as e:
            print(f"❌ Error deleting credentials: {e}")
            return 1


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="AutoTrader Credentials CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List credential profiles')
    list_parser.add_argument('--details', action='store_true',
                           help='Show detailed information')
    
    # Store command
    store_parser = subparsers.add_parser('store', help='Store credentials')
    store_parser.add_argument('profile', help='Profile name')
    store_parser.add_argument('--api-key', help='API Key')
    store_parser.add_argument('--api-secret', help='API Secret')
    store_parser.add_argument('--display-name', help='Display name')
    store_parser.add_argument('--non-interactive', action='store_true',
                            help='Non-interactive mode (requires all parameters)')
    
    # Show command
    show_parser = subparsers.add_parser('show', help='Show credentials')
    show_parser.add_argument('profile', help='Profile name')
    show_parser.add_argument('--show-secret', action='store_true',
                           help='Show API secret (use with caution)')
    
    # Load command
    load_parser = subparsers.add_parser('load', help='Load credentials')
    load_parser.add_argument('profile', help='Profile name')
    load_parser.add_argument('--export', action='store_true',
                           help='Set environment variables')
    
    # Delete command
    delete_parser = subparsers.add_parser('delete', help='Delete credentials')
    delete_parser.add_argument('profile', help='Profile name')
    delete_parser.add_argument('--force', action='store_true',
                             help='Skip confirmation prompt')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    cli = CredentialsCLI()
    
    # Route commands
    if args.command == 'list':
        return cli.list_credentials(args.details)
    elif args.command == 'store':
        return cli.store_credentials(
            args.profile, args.api_key, args.api_secret, args.display_name,
            not args.non_interactive
        )
    elif args.command == 'show':
        return cli.show_credentials(args.profile, args.show_secret)
    elif args.command == 'load':
        return cli.load_credentials(args.profile, args.export)
    elif args.command == 'delete':
        return cli.delete_credentials(args.profile, args.force)
    else:
        print(f"❌ Unknown command: {args.command}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
