#!/usr/bin/env python3
"""
Credentials CLI - Handle credential operations
Replaces complex credential logic from bot.sh
"""

import sys
import os
import json
import getpass
from pathlib import Path
from typing import Dict, List, Optional
import argparse

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))


class CredentialsCLI:
    """Handle credential operations"""
    
    def __init__(self):
        # Support both credential directories
        self.autotrader_creds_dir = Path.home() / '.autotrader' / 'credentials'
        self.traderbot_creds_dir = Path.home() / '.traderbot' / 'credentials'

        # Create directories if they don't exist
        self.autotrader_creds_dir.mkdir(parents=True, exist_ok=True)
        self.traderbot_creds_dir.mkdir(parents=True, exist_ok=True)

        # Default directory for new credentials
        self.creds_dir = self.autotrader_creds_dir

    def _parse_env_file(self, env_file: Path) -> Dict:
        """Parse .env file into credential data"""
        try:
            content = env_file.read_text()
            data = {}

            for line in content.splitlines():
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                if '=' in line:
                    key, value = line.split('=', 1)
                    data[key.strip()] = value.strip()

            # Map to standard format
            result = {
                'profile': env_file.stem,
                'api_key': data.get('BYBIT_API_KEY', ''),
                'api_secret': data.get('BYBIT_API_SECRET', ''),
                'version': 'env'
            }

            # Try to get display name from .display file
            display_file = env_file.parent / f"{env_file.stem}.display"
            if display_file.exists():
                result['display_name'] = display_file.read_text().strip()
            else:
                result['display_name'] = env_file.stem

            return result
        except Exception as e:
            return {
                'profile': env_file.stem,
                'error': str(e),
                'version': 'env'
            }

    def list_credentials(self, show_details: bool = False) -> int:
        """List all credential profiles"""
        try:
            # Get credentials from both directories
            json_files = list(self.autotrader_creds_dir.glob('*.json'))
            env_files = list(self.traderbot_creds_dir.glob('*.env'))

            if not json_files and not env_files:
                print("📭 No credential profiles found")
                print("")
                print("💡 Use '/addcreds' in Telegram bot to add credentials")
                return 0

            print("🔑 Stored Credential Profiles")
            print("=" * 50)

            # Process JSON files
            profiles = []
            for cred_file in sorted(json_files):
                profile_name = cred_file.stem

                try:
                    with open(cred_file, 'r') as f:
                        cred_data = json.load(f)
                    cred_data['source'] = 'json'
                    profiles.append((profile_name, cred_data))
                except Exception as e:
                    print(f"❌ Error reading {profile_name}: {e}")

            # Process ENV files
            for env_file in sorted(env_files):
                profile_name = env_file.stem
                cred_data = self._parse_env_file(env_file)
                cred_data['source'] = 'env'
                profiles.append((profile_name, cred_data))

            # Display profiles (sort by profile name only)
            for profile_name, cred_data in sorted(profiles, key=lambda x: x[0]):
                try:
                    display_name = cred_data.get('display_name', profile_name)
                    api_key = cred_data.get('api_key', '')
                    created = cred_data.get('created', 'Unknown')
                    source = cred_data.get('source', 'unknown')

                    # Use key icon for all valid credentials
                    security_icon = "🔑" if api_key else "⚠️"

                    print(f"{security_icon} {profile_name}")
                    print(f"   Display Name: {display_name}")

                    if show_details:
                        print(f"   API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else '***'}")
                        print(f"   Format: {source}")
                        if created != 'Unknown':
                            print(f"   Created: {created}")

                    print("")

                except Exception as e:
                    print(f"❌ Error displaying {profile_name}: {e}")

            print(f"📊 Total: {len(profiles)} profiles")
            return 0

        except Exception as e:
            print(f"❌ Error listing credentials: {e}")
            return 1
    
    def store_credentials(self, profile: str, api_key: str = None, 
                         api_secret: str = None, display_name: str = None,
                         interactive: bool = True) -> int:
        """Store credentials for a profile"""
        try:
            if interactive:
                if not profile:
                    profile = input("Enter profile name: ").strip()
                
                if not profile:
                    print("❌ Profile name is required")
                    return 1
                
                if not api_key:
                    api_key = input("Enter API Key: ").strip()
                
                if not api_secret:
                    api_secret = getpass.getpass("Enter API Secret: ").strip()
                
                if not display_name:
                    display_name = input(f"Enter display name (default: {profile}): ").strip()
                    if not display_name:
                        display_name = profile
            
            # Validate inputs
            if not all([profile, api_key, api_secret]):
                print("❌ Profile name, API key, and API secret are required")
                return 1
            
            if len(api_key) < 10:
                print("❌ API key seems too short (minimum 10 characters)")
                return 1
            
            if len(api_secret) < 10:
                print("❌ API secret seems too short (minimum 10 characters)")
                return 1
            
            # Store credentials
            cred_data = {
                'profile': profile,
                'api_key': api_key,
                'api_secret': api_secret,
                'display_name': display_name or profile,
                'created': str(Path().cwd()),  # Simple timestamp alternative
                'version': '1.0'
            }
            
            cred_file = self.creds_dir / f"{profile}.json"
            
            # Check if profile exists
            if cred_file.exists():
                if interactive:
                    response = input(f"Profile '{profile}' already exists. Overwrite? [y/N]: ")
                    if response.lower() not in ['y', 'yes']:
                        print("❌ Operation cancelled")
                        return 1
            
            with open(cred_file, 'w') as f:
                json.dump(cred_data, f, indent=2)
            
            # Set secure permissions
            os.chmod(cred_file, 0o600)
            
            print(f"✅ Credentials stored successfully for profile: {profile}")
            print(f"📁 Location: {cred_file}")
            print(f"🔑 API Key: {api_key[:10]}...{api_key[-4:]}")
            
            return 0
            
        except Exception as e:
            print(f"❌ Error storing credentials: {e}")
            return 1
    
    def _find_credential_file(self, profile: str) -> Optional[Path]:
        """Find credential file in either directory"""
        # Check JSON format first
        json_file = self.autotrader_creds_dir / f"{profile}.json"
        if json_file.exists():
            return json_file

        # Check ENV format
        env_file = self.traderbot_creds_dir / f"{profile}.env"
        if env_file.exists():
            return env_file

        return None

    def _load_credential_data(self, cred_file: Path) -> Dict:
        """Load credential data from file (JSON or ENV format)"""
        if cred_file.suffix == '.json':
            with open(cred_file, 'r') as f:
                return json.load(f)
        elif cred_file.suffix == '.env':
            return self._parse_env_file(cred_file)
        else:
            raise ValueError(f"Unsupported credential file format: {cred_file.suffix}")

    def show_credentials(self, profile: str, show_secret: bool = False) -> int:
        """Show credentials for a profile"""
        try:
            cred_file = self._find_credential_file(profile)

            if not cred_file:
                print(f"❌ Profile '{profile}' not found")
                print("")
                print("💡 Available profiles:")
                self.list_credentials()
                return 1

            cred_data = self._load_credential_data(cred_file)

            print(f"🔑 Credentials for profile: {profile}")
            print("=" * 40)
            print(f"Display Name: {cred_data.get('display_name', profile)}")

            api_key = cred_data.get('api_key', '')
            if len(api_key) > 14:
                print(f"API Key: {api_key[:10]}...{api_key[-4:]}")
            else:
                print(f"API Key: {api_key}")

            if show_secret:
                print(f"API Secret: {cred_data.get('api_secret', '')}")
            else:
                print(f"API Secret: {'*' * 20}")

            print(f"Format: {cred_data.get('version', 'unknown')}")
            created = cred_data.get('created', 'Unknown')
            if created != 'Unknown':
                print(f"Created: {created}")

            return 0

        except Exception as e:
            print(f"❌ Error showing credentials: {e}")
            return 1
    
    def load_credentials(self, profile: str, export_env: bool = False) -> int:
        """Load credentials into environment or display export commands"""
        try:
            cred_file = self._find_credential_file(profile)

            if not cred_file:
                print(f"❌ Profile '{profile}' not found")
                return 1

            cred_data = self._load_credential_data(cred_file)

            api_key = cred_data.get('api_key', '')
            api_secret = cred_data.get('api_secret', '')

            if export_env:
                # Set environment variables
                os.environ['BYBIT_API_KEY'] = api_key
                os.environ['BYBIT_API_SECRET'] = api_secret
                print(f"✅ Loaded credentials for profile: {profile}")
                print("🔑 Environment variables set: BYBIT_API_KEY, BYBIT_API_SECRET")
            else:
                # Display export commands
                print(f"🔑 Export commands for profile: {profile}")
                print("=" * 50)
                print(f"export BYBIT_API_KEY='{api_key}'")
                print(f"export BYBIT_API_SECRET='{api_secret}'")
                print("")
                print("💡 Copy and paste these commands to load credentials")

            return 0

        except Exception as e:
            print(f"❌ Error loading credentials: {e}")
            return 1
    
    def delete_credentials(self, profile: str, force: bool = False) -> int:
        """Delete credentials for a profile"""
        try:
            cred_file = self._find_credential_file(profile)

            if not cred_file:
                print(f"❌ Profile '{profile}' not found")
                return 1

            if not force:
                response = input(f"⚠️ Delete credentials for profile '{profile}'? [y/N]: ")
                if response.lower() not in ['y', 'yes']:
                    print("❌ Operation cancelled")
                    return 1

            # Delete main credential file
            cred_file.unlink()

            # If it's an ENV file, also delete the display file
            if cred_file.suffix == '.env':
                display_file = cred_file.parent / f"{profile}.display"
                if display_file.exists():
                    display_file.unlink()

            print(f"✅ Credentials deleted for profile: {profile}")

            return 0

        except Exception as e:
            print(f"❌ Error deleting credentials: {e}")
            return 1


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="AutoTrader Credentials CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List credential profiles')
    list_parser.add_argument('--details', action='store_true',
                           help='Show detailed information')
    
    # Store command
    store_parser = subparsers.add_parser('store', help='Store credentials')
    store_parser.add_argument('profile', help='Profile name')
    store_parser.add_argument('--api-key', help='API Key')
    store_parser.add_argument('--api-secret', help='API Secret')
    store_parser.add_argument('--display-name', help='Display name')
    store_parser.add_argument('--non-interactive', action='store_true',
                            help='Non-interactive mode (requires all parameters)')
    
    # Show command
    show_parser = subparsers.add_parser('show', help='Show credentials')
    show_parser.add_argument('profile', help='Profile name')
    show_parser.add_argument('--show-secret', action='store_true',
                           help='Show API secret (use with caution)')
    
    # Load command
    load_parser = subparsers.add_parser('load', help='Load credentials')
    load_parser.add_argument('profile', help='Profile name')
    load_parser.add_argument('--export', action='store_true',
                           help='Set environment variables')
    
    # Delete command
    delete_parser = subparsers.add_parser('delete', help='Delete credentials')
    delete_parser.add_argument('profile', help='Profile name')
    delete_parser.add_argument('--force', action='store_true',
                             help='Skip confirmation prompt')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    cli = CredentialsCLI()
    
    # Route commands
    if args.command == 'list':
        return cli.list_credentials(args.details)
    elif args.command == 'store':
        return cli.store_credentials(
            args.profile, args.api_key, args.api_secret, args.display_name,
            not args.non_interactive
        )
    elif args.command == 'show':
        return cli.show_credentials(args.profile, args.show_secret)
    elif args.command == 'load':
        return cli.load_credentials(args.profile, args.export)
    elif args.command == 'delete':
        return cli.delete_credentials(args.profile, args.force)
    else:
        print(f"❌ Unknown command: {args.command}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
