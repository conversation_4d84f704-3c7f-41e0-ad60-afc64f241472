#!/bin/bash
# AutoTrader Bot - Compact Version (Server Deployment Script)
# Focused on core deployment and system operations
# Complex logic moved to Python CLI tools

set -euo pipefail

# Version and basic config
SCRIPT_VERSION="3.2.0"
VERSION="$SCRIPT_VERSION"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCKER_CMD="docker"

# ===============================
# Load centralized constants and utilities
# ===============================

# Source shell constants and utilities
source "$SCRIPT_DIR/src/core/shell_constants.sh"

# Auto-load .env file if exists
if [[ -f ".env" ]]; then
    source .env
fi

# Environment variables with defaults
TELEGRAM_BOT_TOKEN="${TELEGRAM_BOT_TOKEN:-}"
TELEGRAM_CHAT_ID="${TELEGRAM_CHAT_ID:-}"

# ===============================
# Additional Utility Functions
# ===============================
# (Core utilities are now in src/core/shell_constants.sh)

check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is not installed or not in PATH"
        echo "Please install Docker and try again"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running"
        echo "Please start Docker and try again"
        return 1
    fi
    
    return 0
}

check_python() {
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 is not installed"
        return 1
    fi
    return 0
}

# Python CLI wrapper - delegates complex operations to Python
run_python_cli() {
    local cli_script="$1"
    shift
    
    if [[ -f ".venv/bin/activate" ]]; then
        source .venv/bin/activate
    fi
    
    python3 "src/cli/${cli_script}.py" "$@"
}

# ===============================
# System Setup Functions
# ===============================

create_default_config() {
    cat > "$CONFIG_DIR/config.json" << 'EOF'
{
  "symbol": "BTC/USDT:USDT",
  "exchange": "bybit",
  "direction": "LONG",
  "amount": 50.0,
  "use_test_mode": false,
  "use_sandbox": false,
  "order_type": "limit",
  "signal_cooldown_minutes": 3.0,
  "trading_loop_interval_seconds": 10,
  "log_level": "INFO",
  "save_trades_to_csv": true,
  "enable_notifications": true,
  "indicators": {
    "ema_periods": [34, 89, 120],
    "timeframes": ["15m", "1h", "4h"],
    "primary_timeframe": "15m",
    "bollinger_bands": {
      "period": 20,
      "std": 2
    },
    "rsi_period": 14,
    "macd": {
      "fast": 12,
      "slow": 26,
      "signal": 9
    },
    "long_signal_threshold": 0.6,
    "short_signal_threshold": 0.6,
    "signal_validation_threshold": 0.6,
    "rsi_oversold_threshold": 30.0,
    "rsi_overbought_threshold": 70.0,
    "bollinger_lower_threshold": 0.2,
    "bollinger_upper_threshold": 0.8,
    "volume_threshold_multiplier": 1.2
  },
  "dca": {
    "enabled": true,
    "max_orders": 3,
    "price_deviation": 1.5,
    "volume_scale": 1.5,
    "strategies": {
      "BB_LOWER": {
        "timeframe": "15m",
        "amount": 50,
        "enabled": true,
        "description": "Buy at Bollinger Lower Band (oversold)"
      },
      "EMA_89": {
        "timeframe": "1h",
        "amount": 50,
        "enabled": false,
        "description": "Buy at EMA 89 (medium-term support)"
      },
      "EMA_34": {
        "timeframe": "15m",
        "amount": 40,
        "enabled": false,
        "description": "Buy at EMA 34 (short-term support)"
      },
      "EMA_120": {
        "timeframe": "4h",
        "amount": 75,
        "enabled": false,
        "description": "Buy at EMA 120 (long-term support)"
      },
      "BB_MIDDLE": {
        "timeframe": "1h",
        "amount": 60,
        "enabled": false,
        "description": "Buy at Bollinger Middle Band (SMA 20)"
      },
      "RSI_OVERSOLD": {
        "timeframe": "15m",
        "amount": 80,
        "enabled": false,
        "description": "Buy when RSI < 30 (extreme oversold)"
      }
    }
  },
  "planned_pair": {
    "long": {
      "take_profit_multiplier": 1.005,
      "stop_loss_multiplier": 0.995
    },
    "short": {
      "take_profit_multiplier": 0.995,
      "stop_loss_multiplier": 1.005
    }
  },
  "risk": {
    "min_position_size": 10.0,
    "max_position_size": 500.0,
    "max_position_percentage": 50.0,
    "leverage": 10.0,
    "daily_loss_limit": 10.0,
    "max_concurrent_positions": 10,
    "default_stop_loss": 2.0,
    "default_take_profit": 3.0,
    "use_trailing_stop": false,
    "trailing_stop_distance": 1.0,
    "tp_sl_strategy": "volatility_based",
    "tp_sl_immediately": true,
    "adjust_tp_sl_on_dca": true,
    "partial_take_profit": false,
    "volatility_base_tp_percent": 1.0,
    "volatility_base_sl_percent": 0.5,
    "volatility_tp_multiplier": 0.4,
    "volatility_sl_multiplier": 0.25,
    "risk_per_trade": 20.0,
    "use_kelly_criterion": false,
    "loss_cooldown_minutes": 30,
    "max_daily_trades": 10
  }
}
EOF
}

setup_environment() {
    echo "🔧 AutoTrader Complete Setup"
    echo "============================"

    # Check and install Docker (Linux only)
    if ! command -v docker &> /dev/null; then
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            echo "🐳 Installing Docker..."
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
            rm get-docker.sh
            echo "✅ Docker installed. Please log out and back in."
        else
            echo "❌ Please install Docker manually for your OS"
            return 1
        fi
    fi

    # Verify Docker is running
    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running. Please start Docker."
        return 1
    else
        echo "✅ Docker is running"
    fi

    # Create directories and config files
    echo "📁 Creating directories and config files..."
    ensure_directories

    # Create default config file
    if [[ ! -f "$CONFIG_DIR/config.json" ]]; then
        create_default_config
        echo "✅ Created default config: $CONFIG_DIR/config.json"
    else
        echo "✅ Default config already exists"
    fi

    # Pull Docker images if Docker is available
    if check_docker; then
        echo "🐳 Pulling Docker images..."
        docker pull "$TELEGRAM_IMAGE" 2>/dev/null || echo "⚠️ Could not pull Telegram image"
        docker pull "$TRADER_IMAGE" 2>/dev/null || echo "⚠️ Could not pull Trader image"
    fi

    # Check Telegram setup
    echo "📱 Checking Telegram configuration..."
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]] || [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        echo "⚠️ Telegram not configured. Set environment variables:"
        echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
        echo "   export TELEGRAM_CHAT_ID='your_chat_id'"
    else
        echo "✅ Telegram configured"
    fi

    echo ""
    echo "🎉 Docker setup finished!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Configure Telegram (if not done):"
    echo "   export TELEGRAM_BOT_TOKEN='your_token'"
    echo "   export TELEGRAM_CHAT_ID='your_chat_id'"
    echo "2. Start Telegram bot:"
    echo "   ./bot.sh telegram"
    echo "3. Use Telegram commands to manage trading bots"
}

upgrade_script() {
    echo "🔄 Upgrading bot.sh from GitHub Gist..."
    echo "========================================"

    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        echo "❌ curl is required for upgrade. Please install curl first."
        return 1
    fi

    local gist_url="${BOT_GIST_URL:-https://gist.githubusercontent.com/hoangtrung99/73593690940ff91015063f2b6f9366a3/raw/autotrader.sh}"
    local script_path="$(realpath "${BASH_SOURCE[0]}")"
    local backup_file="${script_path}.backup.$(date +%Y%m%d-%H%M%S)"

    echo "📄 Current script: $script_path"
    echo "💾 Backup will be saved to: $backup_file"
    echo "🌐 Gist URL: $gist_url"

    # Create backup
    if cp "$script_path" "$backup_file"; then
        echo "✅ Backup created: $backup_file"
    else
        echo "❌ Failed to create backup. Aborting upgrade."
        return 1
    fi

    # Download new version to temporary file
    echo ""
    echo "📥 Downloading latest version..."
    local temp_file=$(mktemp)

    if curl -fsSL "$gist_url" -o "$temp_file"; then
        echo "✅ Downloaded successfully"

        # Show version comparison if possible
        local current_version=$(grep "^SCRIPT_VERSION=" "$script_path" | cut -d'"' -f2 2>/dev/null || echo "unknown")
        local new_version=$(grep "^SCRIPT_VERSION=" "$temp_file" | cut -d'"' -f2 2>/dev/null || echo "unknown")

        echo ""
        echo "📊 Version Comparison:"
        echo "Current: $current_version"
        echo "New:     $new_version"

        # Confirm upgrade
        echo ""
        echo "❓ Do you want to proceed with the upgrade? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            echo "❌ Upgrade cancelled"
            rm -f "$temp_file"
            return 0
        fi

        # Replace current script with new version
        echo ""
        echo "🔄 Installing new version..."
        if mv "$temp_file" "$script_path"; then
            chmod +x "$script_path"
            echo "✅ Upgrade completed successfully!"
            echo ""
            echo "🎉 Upgrade completed!"
            echo "========================================"
            echo "📄 New version installed: $script_path"
            echo "💾 Backup available: $backup_file"
            echo ""
            echo "🔍 To verify the upgrade worked:"
            echo "   ./bot.sh version"
        else
            echo "❌ Failed to install new version"
            echo "🔄 Restoring from backup..."
            if cp "$backup_file" "$script_path"; then
                echo "✅ Restored from backup"
            else
                echo "❌ Failed to restore backup!"
            fi
            return 1
        fi
    else
        echo "❌ Failed to download new version"
        return 1
    fi
}

bind_global_command() {
    echo "🔗 Binding bot.sh as global 'traderbot' command..."
    echo "========================================"

    # Detect OS for different approaches
    local os="unknown"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        os="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        os="macos"
    fi

    local script_path="$(realpath "${BASH_SOURCE[0]}")"
    local installed=false

    # Try different installation paths
    local install_paths=(
        "$HOME/.local/bin/traderbot"
        "$HOME/bin/traderbot"
        "/usr/local/bin/traderbot"
    )

    for install_path in "${install_paths[@]}"; do
        local install_dir=$(dirname "$install_path")

        # Create directory if it doesn't exist
        if [[ ! -d "$install_dir" ]]; then
            if mkdir -p "$install_dir" 2>/dev/null; then
                echo "✅ Created directory: $install_dir"
            else
                echo "❌ Cannot create directory: $install_dir"
                continue
            fi
        fi

        # Try to install
        if cp "$script_path" "$install_path" 2>/dev/null; then
            chmod +x "$install_path"
            echo "✅ Installed to: $install_path"

            # Add to PATH if needed
            if [[ ":$PATH:" != *":$install_dir:"* ]]; then
                echo "💡 Adding $install_dir to PATH..."

                # Determine shell profile
                local shell_profile=""
                if [[ -n "$ZSH_VERSION" ]]; then
                    shell_profile="$HOME/.zshrc"
                elif [[ -n "$BASH_VERSION" ]]; then
                    shell_profile="$HOME/.bashrc"
                fi

                if [[ -n "$shell_profile" ]]; then
                    echo "export PATH=\"$install_dir:\$PATH\"" >> "$shell_profile"
                    echo "✅ Added to $shell_profile"
                    echo "💡 Run: source $shell_profile"
                fi
            fi

            installed=true
            break
        else
            echo "❌ Failed to install to: $install_path"
        fi
    done

    if [[ "$installed" == "true" ]]; then
        echo ""
        echo "🎉 Global 'traderbot' command installed!"
        echo "========================================"
        echo ""
        echo "📋 Usage Examples:"
        echo "  traderbot setup                    # Setup environment"
        echo "  traderbot start eth --amount 100   # Start ETH bot"
        echo "  traderbot list                     # List containers"
        echo "  traderbot telegram                 # Start Telegram bot"
        echo ""
        echo "💡 You may need to restart your terminal or run:"
        echo "   source ~/.bashrc  # or ~/.zshrc"
    else
        echo ""
        echo "❌ Failed to install global command"
        echo "💡 Manual installation:"
        echo "   sudo cp $script_path /usr/local/bin/traderbot"
        echo "   sudo chmod +x /usr/local/bin/traderbot"
    fi
}

unbind_global_command() {
    echo "🗑️  Removing 'traderbot' global command..."
    echo "========================================"

    # List of possible installation paths
    local remove_paths=(
        "/usr/local/bin/traderbot"
        "$HOME/.local/bin/traderbot"
        "$HOME/bin/traderbot"
    )

    local removed=false

    for remove_path in "${remove_paths[@]}"; do
        if [[ -f "$remove_path" ]]; then
            if rm "$remove_path" 2>/dev/null; then
                echo "✅ Removed: $remove_path"
                removed=true
            else
                echo "❌ Failed to remove: $remove_path (try with sudo)"
            fi
        fi
    done

    if [[ "$removed" == "true" ]]; then
        echo ""
        echo "✅ Global 'traderbot' command removed"
        echo "💡 You can still use: ./bot.sh"
    else
        echo ""
        echo "ℹ️  No global 'traderbot' command found"
    fi
}

# ===============================
# Docker Operations
# ===============================

start_telegram_bot() {
    echo "📱 Starting Telegram Bot (Docker)"
    echo "================================="

    # Check Docker
    if ! check_docker; then
        echo "❌ Docker is required to run Telegram bot"
        return 1
    fi

    # Check environment
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]] || [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        echo "❌ Missing Telegram credentials"
        echo ""
        echo "💡 Set environment variables:"
        echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
        echo "   export TELEGRAM_CHAT_ID='your_chat_id'"
        return 1
    fi

    # Check if container already exists
    if docker ps -a --format "{{.Names}}" | grep -q "^telegram-bot$"; then
        echo "🔄 Stopping existing Telegram bot container..."
        docker stop telegram-bot 2>/dev/null || true
        docker rm telegram-bot 2>/dev/null || true
    fi

    # Start Telegram bot container
    echo "🐳 Starting Telegram bot container..."

    # Ensure directories exist
    ensure_directories

    docker run -d \
        --name telegram-bot \
        --restart unless-stopped \
        -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
        -e TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID" \
        -v "$CONFIG_DIR:/app/configs" \
        -v "$DATA_DIR:/app/data" \
        -v "$LOGS_DIR:/app/logs" \
        -v "$CREDENTIALS_DIR:/root/.autotrader/credentials" \
        -v /var/run/docker.sock:/var/run/docker.sock \
        "$TELEGRAM_IMAGE"

    if [[ $? -eq 0 ]]; then
        echo "✅ Telegram bot started successfully!"
        echo "📊 Container: telegram-bot"
        echo "🔗 Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
        echo "💬 Chat ID: $TELEGRAM_CHAT_ID"
        echo ""
        echo "📋 Monitor with:"
        echo "   docker logs telegram-bot"
        echo "   ./bot.sh simple-logs telegram-bot"
    else
        echo "❌ Failed to start Telegram bot container"
        return 1
    fi
}



start_trading_bot() {
    local symbol="$1"
    shift

    echo "🤖 Starting Trading Bot: $symbol"
    echo "================================"

    # Validate symbol
    if [[ -z "$symbol" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: ./bot.sh start <symbol> [options]"
        return 1
    fi

    # Normalize symbol and generate container name
    local full_symbol=$(normalize_symbol "$symbol")
    local container_name=$(get_container_name "$symbol")

    # Check if container already exists
    if docker ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
        echo "❌ Container '$container_name' already exists"
        echo "💡 Use: ./bot.sh restart $symbol"
        return 1
    fi

    # Create symbol-specific config
    local config_file="$CONFIG_DIR/${container_name}.json"
    if [[ ! -f "$config_file" ]]; then
        echo "📝 Creating config for $symbol..."

        # Create config from template (escape special characters for sed)
        local escaped_symbol=$(echo "$full_symbol" | sed 's/[\/&]/\\&/g')
        sed "s/SYMBOL_PLACEHOLDER/$escaped_symbol/g" "$CONFIG_DIR/template.json" > "$config_file"
        echo "✅ Created config: $config_file"
    fi

    # Parse additional options
    local amount="50"
    local test_mode="false"
    local direction="LONG"
    local profile=""
    local cmd_api_key=""
    local cmd_api_secret=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --amount)
                amount="$2"
                shift 2
                ;;
            --test-mode|--test)
                test_mode="true"
                shift
                ;;
            --live-mode|--live)
                test_mode="false"
                shift
                ;;
            --direction)
                direction="$2"
                shift 2
                ;;
            --profile)
                profile="$2"
                shift 2
                ;;
            --api-key)
                cmd_api_key="$2"
                shift 2
                ;;
            --api-secret)
                cmd_api_secret="$2"
                shift 2
                ;;
            *)
                echo "⚠️ Unknown option: $1"
                shift
                ;;
        esac
    done

    # Resolve credentials using priority system
    if ! resolve_credentials "$profile" "$cmd_api_key" "$cmd_api_secret"; then
        echo ""
        echo "💡 Available credential sources (in priority order):"
        echo "   1. Profile: --profile <name> (highest priority)"
        echo "   2. Command line: --api-key <key> --api-secret <secret>"
        echo "   3. Environment: BYBIT_API_KEY, BYBIT_API_SECRET/BYBIT_SECRET_KEY"
        echo ""
        echo "📋 Examples:"
        echo "   ./bot.sh start btc --profile main --amount 100"
        echo "   ./bot.sh start btc --api-key 'key' --api-secret 'secret'"
        echo "   export BYBIT_API_KEY='key' && ./bot.sh start btc"
        echo ""
        echo "💡 Manage profiles:"
        echo "   python3 src/cli/credentials_cli.py list"
        echo "   python3 src/cli/credentials_cli.py store <profile>"
        return 1
    fi

    # Verify credentials are now available
    local api_key="${!BYBIT_API_KEY_VAR:-}"
    local api_secret="${!BYBIT_API_SECRET_VAR:-}"

    if [[ -z "$api_key" ]] || [[ -z "$api_secret" ]]; then
        echo "❌ Credential resolution failed"
        return 1
    fi

    # Check Docker is available
    if ! check_docker; then
        echo "❌ Docker is required to run trading bots"
        return 1
    fi

    # Start Docker container
    echo "🐳 Starting Docker container..."

    # Prepare Docker command arguments
    local docker_args=(
        "run" "-d"
        "--name" "$container_name"
        "--restart" "unless-stopped"
        "-e" "$BYBIT_API_KEY_VAR=$api_key"
        "-e" "$BYBIT_API_SECRET_VAR=$api_secret"
        "-e" "BOT_CONFIG_FILE=configs/${container_name}.json"
        "-e" "TRADE_SYMBOL=$full_symbol"
        "-e" "TRADE_AMOUNT=$amount"
        "-e" "TRADE_DIRECTION=$direction"
        "-e" "TEST_MODE=$test_mode"
        "-v" "$CONFIG_DIR:/app/configs"
        "-v" "$DATA_DIR:/app/data"
        "-v" "$LOGS_DIR:/app/logs"
        "$TRADER_IMAGE"
        "python" "main.py" "--start"
    )

    # Add test flag if in test mode
    if [[ "$test_mode" == "true" ]]; then
        docker_args+=("--test")
    fi

    # Run Docker container
    docker "${docker_args[@]}"

    if [[ $? -eq 0 ]]; then
        echo "✅ Trading bot started successfully!"
        echo "📊 Container: $container_name"
        echo "💰 Symbol: $full_symbol"
        echo "💵 Amount: $amount USDT"
        echo "🧪 Test Mode: $test_mode"
        echo ""
        echo "📋 Monitor with:"
        echo "   ./bot.sh logs $container_name"
        echo "   python3 src/cli/autotrader_cli.py status $container_name"
    else
        echo "❌ Failed to start trading bot"
        return 1
    fi
}

start_all() {
    echo "🚀 Starting Complete AutoTrader System"
    echo "======================================"
    
    # Start Telegram bot in background
    echo "📱 Starting Telegram bot..."
    start_telegram_bot &
    
    echo "✅ System startup initiated"
    echo "📱 Telegram bot running in background"
    echo "💡 Use Telegram commands to manage trading bots"
}

stop_all() {
    echo "🛑 Stopping All AutoTrader Services"
    
    # Stop all containers
    if check_docker; then
        echo "🐳 Stopping Docker containers..."
        $DOCKER_CMD stop $(docker ps -q --filter "name=autotrader") 2>/dev/null || true
        echo "✅ All containers stopped"
    fi
    
    # Kill background processes
    pkill -f "telegram" 2>/dev/null || true
    
    echo "✅ All services stopped"
}

system_status() {
    echo "📊 AutoTrader System Status"
    echo "==========================="

    # Docker status
    if check_docker; then
        echo "✅ Docker: Running"
        local containers=$($DOCKER_CMD ps --format "table {{.Names}}\t{{.Status}}" | grep autotrader || echo "No containers")
        echo "🐳 Containers: $containers"
    else
        echo "❌ Docker: Not available"
    fi

    # Python status
    if check_python; then
        echo "✅ Python: $(python3 --version)"
    else
        echo "❌ Python: Not available"
    fi

    # Telegram status
    if [[ -n "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "✅ Telegram: Configured"
    else
        echo "⚠️ Telegram: Not configured"
    fi
}

simple_logs() {
    local container_name="$1"
    local lines="${2:-50}"

    if [[ -z "$container_name" ]]; then
        echo "❌ Container name is required"
        return 1
    fi

    echo "📋 Logs for container: $container_name (last $lines lines)"
    echo "=" * 60

    if docker logs "$container_name" --tail "$lines" 2>/dev/null; then
        echo ""
        echo "✅ Logs retrieved successfully"
    else
        echo "❌ Failed to get logs for container: $container_name"
        echo "💡 Available containers:"
        docker ps --format "{{.Names}}" | head -5
        return 1
    fi
}

# ===============================
# Credential Management Functions
# ===============================

check_telegram_profile() {
    # Check if Telegram bot has set a profile for this session
    local telegram_profile_file="/tmp/traderbot_telegram_profile_$$"
    local telegram_profile_global="/tmp/traderbot_telegram_profile"

    if [[ -f "$telegram_profile_file" ]]; then
        cat "$telegram_profile_file"
        return 0
    elif [[ -f "$telegram_profile_global" ]]; then
        cat "$telegram_profile_global"
        return 0
    fi

    return 1
}

load_profile_credentials() {
    local profile="$1"

    if [[ -z "$profile" ]]; then
        return 1
    fi

    echo "🔄 Loading credentials from profile: $profile"

    # Use centralized credentials directory
    local creds_dir="$CREDENTIALS_DIR"

    # Try Python CLI first (JSON format)
    local json_file="$creds_dir/${profile}.json"
    if [[ -f "$json_file" ]]; then
        # Load from JSON format (Python CLI) - get export commands
        if command -v python3 &> /dev/null; then
            local result=$(python3 src/cli/credentials_cli.py load "$profile" 2>/dev/null)
            if [[ $? -eq 0 ]]; then
                # Parse the export commands
                local api_key=$(echo "$result" | grep "export $BYBIT_API_KEY_VAR=" | cut -d"'" -f2)
                local api_secret=$(echo "$result" | grep "export $BYBIT_API_SECRET_VAR=" | cut -d"'" -f2)

                if [[ -n "$api_key" ]] && [[ -n "$api_secret" ]]; then
                    export "$BYBIT_API_KEY_VAR"="$api_key"
                    export "$BYBIT_API_SECRET_VAR"="$api_secret"
                    echo "✅ Loaded credentials from profile: $profile"
                    return 0
                fi
            fi
        fi
    fi

    # Try legacy .env format (for backward compatibility)
    local env_file="$creds_dir/${profile}.env"
    if [[ -f "$env_file" ]]; then
        echo "🔄 Loading from .env format..."
        source "$env_file"
        if [[ -n "${!BYBIT_API_KEY_VAR}" ]] && [[ -n "${!BYBIT_API_SECRET_VAR}" ]]; then
            echo "✅ Loaded credentials from profile: $profile"
            return 0
        fi
    fi

    echo "❌ Failed to load credentials from profile: $profile"
    return 1
}

resolve_credentials() {
    local profile="$1"
    local cmd_api_key="$2"
    local cmd_api_secret="$3"

    echo "🔍 Resolving credentials with priority system..."

    # Priority 1: Profile credentials (from Telegram bot selection or command line)
    # Check Telegram profile first if no explicit profile provided
    if [[ -z "$profile" ]]; then
        local telegram_profile=$(check_telegram_profile 2>/dev/null)
        if [[ -n "$telegram_profile" ]]; then
            echo "🥇 Priority 1a: Found Telegram selected profile '$telegram_profile'"
            profile="$telegram_profile"
        fi
    fi

    if [[ -n "$profile" ]]; then
        echo "🥇 Priority 1b: Loading from profile '$profile'"
        if load_profile_credentials "$profile"; then
            return 0
        else
            echo "⚠️ Failed to load profile '$profile', trying next priority..."
        fi
    fi

    # Priority 2: Command line arguments
    if [[ -n "$cmd_api_key" ]] && [[ -n "$cmd_api_secret" ]]; then
        echo "🥈 Priority 2: Using command line credentials"
        export "$BYBIT_API_KEY_VAR"="$cmd_api_key"
        export "$BYBIT_API_SECRET_VAR"="$cmd_api_secret"
        echo "✅ Loaded credentials from command line"
        return 0
    fi

    # Priority 3: Environment variables
    local env_api_key="${!BYBIT_API_KEY_VAR:-}"
    local env_api_secret="${!BYBIT_API_SECRET_VAR:-${!BYBIT_SECRET_KEY_VAR:-}}"

    if [[ -n "$env_api_key" ]] && [[ -n "$env_api_secret" ]]; then
        echo "🥉 Priority 3: Using environment variables"
        export "$BYBIT_API_KEY_VAR"="$env_api_key"
        export "$BYBIT_API_SECRET_VAR"="$env_api_secret"
        echo "✅ Loaded credentials from environment"
        return 0
    fi

    # No credentials found
    echo "❌ No credentials found in any source"
    return 1
}

show_help() {
    cat << EOF
🤖 AutoTrader Bot - Compact Server Deployment Script

USAGE: $0 <command> [options]

📋 DEPLOYMENT COMMANDS:
    setup                    Complete environment setup (creates configs, directories)
    upgrade                  Upgrade script from GitHub Gist
    bind                     Install as global 'traderbot' command
    unbind                   Remove global 'traderbot' command
    start-all               Start complete system
    stop-all                Stop all services
    system-status           Show system status

📱 TELEGRAM BOT:
    telegram                Start Telegram bot (curl-based, minimal dependencies)

🚀 TRADING BOTS:
    start <symbol> [opts]   Create trading bot

    Options:
      --amount <amount>     Trading amount (default: 50)
      --test               Enable test mode
      --live               Enable live mode (default)
      --profile <name>     Use credential profile (highest priority)
      --api-key <key>      API key (medium priority)
      --api-secret <secret> API secret (medium priority)
      --direction <dir>    Trading direction (LONG/SHORT)

📊 BOT MANAGEMENT (Use Python CLI or Telegram):
    
    Python CLI:
    python3 src/cli/autotrader_cli.py list
    python3 src/cli/autotrader_cli.py status <symbol>
    python3 src/cli/autotrader_cli.py logs <symbol>
    python3 src/cli/autotrader_cli.py stop <symbol>
    
    Telegram Commands:
    /list, /status, /logs, /stop, /restart, /remove

🔑 CREDENTIALS (Use Python CLI):
    python3 src/cli/credentials_cli.py list
    python3 src/cli/credentials_cli.py store <profile>
    python3 src/cli/credentials_cli.py load <profile>

EXAMPLES:
    # First time setup
    $0 setup                    # Complete environment setup
    $0 bind                     # Install as 'traderbot' command (optional)

    # Trading operations (credential priority examples)
    $0 start eth --profile main --amount 100        # Use 'main' profile (highest priority)
    $0 start btc --api-key 'key' --api-secret 'sec' # Use command line credentials
    $0 start hyper --amount 50 --test               # Use environment variables + test mode

    # Traditional examples
    $0 start eth --amount 100   # Start ETH trading bot (live mode)
    $0 start btc --test         # Start BTC bot in test mode

    # System management
    $0 start-all               # Start complete system
    $0 telegram                # Start Telegram bot
    $0 system-status           # Check system status

    # Global command usage (after bind)
    traderbot start eth --amount 100
    traderbot list
    traderbot telegram

EOF
}

# ===============================
# Main Command Router
# ===============================

main() {
    case "${1:-help}" in
        # Deployment commands
        setup) setup_environment ;;
        upgrade) upgrade_script ;;
        bind) bind_global_command ;;
        unbind) unbind_global_command ;;
        start-all) start_all ;;
        stop-all) stop_all ;;
        system-status) system_status ;;
        
        # Telegram commands
        telegram) start_telegram_bot ;;
        telegram-deploy)
            echo "⚠️ 'telegram-deploy' is deprecated. Use 'telegram' for simple bot."
            echo "💡 For Docker deployment, use: docker run with manual setup"
            start_telegram_bot
            ;;
        
        # Trading commands
        start) shift; start_trading_bot "$@" ;;
        
        # Delegated commands (use Python CLI)
        list) 
            echo "⚠️ Use Python CLI: python3 src/cli/autotrader_cli.py list"
            run_python_cli "autotrader_cli" list
            ;;
        status)
            echo "⚠️ Use Python CLI: python3 src/cli/autotrader_cli.py status $2"
            run_python_cli "autotrader_cli" status "$2"
            ;;
        logs)
            if [[ -z "$2" ]]; then
                echo "❌ Symbol is required"
                echo "Usage: ./bot.sh logs <symbol> [lines]"
                exit 1
            fi
            echo "⚠️ Use Python CLI: python3 src/cli/autotrader_cli.py logs $2"
            run_python_cli "autotrader_cli" logs "$2" "${3:-50}"
            ;;
        simple-logs)
            simple_logs "$2" "$3"
            ;;
        stop)
            echo "⚠️ Use Python CLI: python3 src/cli/autotrader_cli.py stop $2"
            run_python_cli "autotrader_cli" stop "$2"
            ;;
        restart)
            echo "⚠️ Use Python CLI: python3 src/cli/autotrader_cli.py restart $2"
            run_python_cli "autotrader_cli" restart "$2"
            ;;
        
        # Credential commands
        list-credentials)
            run_python_cli "credentials_cli" list
            ;;
        store-credentials)
            shift # Remove "store-credentials"
            profile="$1"
            api_key="$2"
            api_secret="$3"
            display_name="$4"

            if [[ -z "$profile" || -z "$api_key" || -z "$api_secret" ]]; then
                echo "❌ Usage: ./bot.sh store-credentials <profile> <api_key> <api_secret> [display_name]"
                exit 1
            fi

            # Use non-interactive mode with all parameters
            run_python_cli "credentials_cli" store "$profile" --api-key "$api_key" --api-secret "$api_secret" --display-name "$display_name" --non-interactive
            ;;
        load-credentials)
            run_python_cli "credentials_cli" load "$2"
            ;;
        
        # Utility commands
        help|--help|-h) show_help ;;
        version|--version|-v)
            echo "🤖 AutoTrader Bot v$SCRIPT_VERSION"
            echo "📅 Compact & Enhanced Version"
            echo "🐳 Docker-based Trading System"
            ;;
        check)
            echo "🔍 System Check"
            echo "==============="
            check_docker && echo "✅ Docker: Available" || echo "❌ Docker: Not available"
            check_python && echo "✅ Python: Available" || echo "❌ Python: Not available"
            [[ -n "$TELEGRAM_BOT_TOKEN" ]] && echo "✅ Telegram: Configured" || echo "⚠️ Telegram: Not configured"
            echo "📁 Configs: $(ls $CONFIG_DIR/*.json 2>/dev/null | wc -l) files"
            echo "🐳 Containers: $(docker ps -q | wc -l) running"
            ;;
        
        *)
            echo "❌ Unknown command: $1"
            echo "💡 Use '$0 help' for available commands"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
