#!/bin/bash
# Simplified Trading Bot Manager - CLI Interface Only
# All logic moved to Python modules

set -e

# Script Version
SCRIPT_VERSION="3.0.0"
VERSION_CHECK_ENABLED=${BOT_VERSION_CHECK:-true}

# Configuration
REGISTRY=${DOCKER_REGISTRY:-"ghcr.io/hoangtrung99"}
IMAGE_BASE=${IMAGE_BASE:-"autotrader"}
VERSION=${VERSION:-"latest"}

# Define separate images for telegram and trader
# Use local images for testing
TELEGRAM_IMAGE="autotrader-telegram:latest"
TRADER_IMAGE="autotrader-trader:latest"

# Backward compatibility
FULL_IMAGE="$TRADER_IMAGE"  # Default to trader image for existing functions

# Telegram Configuration
TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN:-""}
TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID:-""}
TELEGRAM_ENABLED=${TELEGRAM_ENABLED:-true}

# Check if Telegram is available
check_telegram_available() {
    if [[ "$TELEGRAM_ENABLED" != "true" ]]; then
        return 1
    fi
    
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]] || [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        return 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        return 1
    fi
    
    return 0
}

# Send Telegram notification
send_telegram_notification() {
    local message="$1"
    local command="${2:-""}"
    local returncode="${3:-0}"
    local stdout="${4:-""}"
    local stderr="${5:-""}"

    if ! check_telegram_available; then
        return 0
    fi

    # Temporarily disabled to avoid dependency issues
    # TODO: Fix telegram notification system
    echo "📱 Telegram notification: $message" >&2
    return 0

    # Activate virtual environment if available
    if [[ -f ".venv/bin/activate" ]]; then
        source .venv/bin/activate
    fi

    if [[ -n "$command" ]]; then
        # Send command response
        python run_telegram_app.py send \
            --token "$TELEGRAM_BOT_TOKEN" \
            --chat-id "$TELEGRAM_CHAT_ID" \
            --command "$command" \
            --returncode "$returncode" \
            --stdout "$stdout" \
            --stderr "$stderr" &
    else
        # Send simple message
        python run_telegram_app.py send \
            --token "$TELEGRAM_BOT_TOKEN" \
            --chat-id "$TELEGRAM_CHAT_ID" \
            --message "$message" &
    fi
}

# Send Telegram message with HTML formatting
telegram_send_message() {
    local chat_id="$1"
    local text="$2"
    local parse_mode="${3:-HTML}"
    local reply_markup="$4"

    # Check if required tools are available
    if ! command -v curl &> /dev/null || ! command -v jq &> /dev/null; then
        echo "⚠️ curl and jq are required for Telegram messaging" >&2
        return 1
    fi

    # Sanitize text for HTML parsing
    local clean_text="$text"

    # If using HTML, escape only essential HTML characters
    if [[ "$parse_mode" == "HTML" ]]; then
        # Escape HTML special characters (but preserve our intended HTML tags)
        clean_text=$(echo "$clean_text" | sed 's/&/\&amp;/g')
        clean_text=$(echo "$clean_text" | sed 's/</\&lt;/g' | sed 's/>/\&gt;/g')
        # Restore our intended HTML tags
        clean_text=$(echo "$clean_text" | sed 's/\&lt;b\&gt;/<b>/g' | sed 's/\&lt;\/b\&gt;/<\/b>/g')
        clean_text=$(echo "$clean_text" | sed 's/\&lt;i\&gt;/<i>/g' | sed 's/\&lt;\/i\&gt;/<\/i>/g')
        clean_text=$(echo "$clean_text" | sed 's/\&lt;code\&gt;/<code>/g' | sed 's/\&lt;\/code\&gt;/<\/code>/g')
        clean_text=$(echo "$clean_text" | sed 's/\&lt;pre\&gt;/<pre>/g' | sed 's/\&lt;\/pre\&gt;/<\/pre>/g')
    fi

    # Escape for JSON - use jq for safer escaping
    local escaped_text=$(printf '%s' "$clean_text" | jq -R -s '.')
    escaped_text="${escaped_text%\"}"  # Remove trailing quote
    escaped_text="${escaped_text#\"}"  # Remove leading quote

    local json_data="{\"chat_id\":\"$chat_id\",\"text\":\"$escaped_text\",\"parse_mode\":\"$parse_mode\""

    if [[ -n "$reply_markup" ]]; then
        json_data+=",\"reply_markup\":$reply_markup"
    fi

    json_data+="}"

    # Send with error handling
    local response=$(curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -H "Content-Type: application/json" \
        -d "$json_data")

    # Check for errors
    if echo "$response" | jq -e '.ok' >/dev/null 2>&1; then
        return 0
    else
        local error_desc=$(echo "$response" | jq -r '.description // "Unknown error"')
        echo "❌ Failed to send message: $error_desc" >&2

        # If HTML failed, try with plain text as fallback
        if [[ "$parse_mode" == "HTML" ]]; then
            echo "⚠️ HTML parsing failed, retrying with plain text" >&2
            # Strip HTML tags and try again
            local plain_text=$(echo "$text" | sed 's/<[^>]*>//g')
            telegram_send_message "$chat_id" "$plain_text" "" "$reply_markup"
        else
            return 1
        fi
    fi
}

# Version check and enforcement (simplified)
check_version_and_enforce_upgrade() {
    if [[ "$VERSION_CHECK_ENABLED" != "true" ]] || [[ "$1" == "upgrade" ]] || [[ "$1" == "help" ]] || [[ "$1" == "" ]]; then
        return 0
    fi
    
    echo "✅ Version check passed (v$SCRIPT_VERSION)"
}

# Execute Python command with proper error handling and response formatting
execute_python_command() {
    local command=("$@")

    # Activate virtual environment if available
    if [[ -f "/opt/venv/bin/activate" ]]; then
        source /opt/venv/bin/activate
    elif [[ -f ".venv/bin/activate" ]]; then
        source .venv/bin/activate
    fi

    # Execute the Python command
    local temp_stdout=$(mktemp)
    local temp_stderr=$(mktemp)

    "${command[@]}" >"$temp_stdout" 2>"$temp_stderr"
    local returncode=$?

    local stdout=$(cat "$temp_stdout")
    local stderr=$(cat "$temp_stderr")

    # Cleanup temp files
    rm -f "$temp_stdout" "$temp_stderr"

    if [[ $returncode -eq 0 ]]; then
        if [[ -n "$stdout" ]]; then
            echo "$stdout"
        fi
    else
        local error_msg="$stderr"
        [[ -z "$error_msg" ]] && error_msg="$stdout"
        [[ -z "$error_msg" ]] && error_msg="Command failed with exit code $returncode"
        echo "❌ Error: $error_msg"
    fi

    return $returncode
}

# Check Docker availability
check_docker() {
    # If running inside Docker container, assume Docker is available via socket
    if [ -f /.dockerenv ]; then
        echo "🐳 Running inside Docker container"
        # Always use sudo in container since docker socket permissions are complex
        export DOCKER_CMD="sudo docker"

        # Test the docker command
        if ! $DOCKER_CMD version >/dev/null 2>&1; then
            echo "❌ Cannot connect to Docker daemon."
            return 1
        fi
        return 0
    fi

    # Check if Docker socket is available
    if [ -S /var/run/docker.sock ]; then
        export DOCKER_CMD="docker"
        if ! $DOCKER_CMD version >/dev/null 2>&1; then
            echo "❌ Cannot connect to Docker daemon."
            return 1
        fi
        return 0
    fi

    if ! command -v docker >/dev/null 2>&1; then
        echo "❌ Docker not found. Please install Docker."
        return 1
    fi

    export DOCKER_CMD="docker"
    if ! $DOCKER_CMD version >/dev/null 2>&1; then
        echo "❌ Cannot connect to Docker daemon."
        return 1
    fi

    return 0
}

# Generate container name from symbol with enhanced mapping
generate_container_name() {
    local symbol="$1"
    if [[ -z "$symbol" ]]; then
        echo "crypto-trading-bot"
        return
    fi

    # Handle different symbol formats
    local base_symbol=""

    if [[ "$symbol" == *"/"* ]]; then
        # Full format like "ETH/USDT:USDT" -> "eth"
        base_symbol=$(echo "$symbol" | cut -d'/' -f1)
    elif [[ "$symbol" == *"USDT" ]] && [[ ${#symbol} -gt 4 ]]; then
        # Format like "ETHUSDT" -> "eth"
        base_symbol="${symbol%USDT}"
    else
        # Simple format like "ETH" or "eth" -> "eth"
        base_symbol="$symbol"
    fi

    # Normalize to lowercase
    local normalized=$(echo "$base_symbol" | tr '[:upper:]' '[:lower:]')
    echo "$normalized"
}

# Find container by symbol (enhanced search)
find_container_by_symbol() {
    local symbol="$1"
    if [[ -z "$symbol" ]]; then
        return 1
    fi

    # Generate expected container name
    local expected_name=$(generate_container_name "$symbol")

    # First try exact match
    if $DOCKER_CMD ps -a --format "{{.Names}}" 2>/dev/null | grep -q "^${expected_name}$"; then
        echo "$expected_name"
        return 0
    fi

    # Try fuzzy search for containers containing the symbol
    local containers=$($DOCKER_CMD ps -a --format "{{.Names}}" 2>/dev/null | grep -i "$expected_name" | head -1)
    if [[ -n "$containers" ]]; then
        echo "$containers"
        return 0
    fi

    # No container found
    return 1
}

# Parse trading arguments
parse_trading_arguments() {
    TRADE_SYMBOL=""
    TRADE_AMOUNT=""
    TRADE_EXCHANGE=""
    TRADE_DIRECTION=""
    TRADE_TEST_MODE=""
    TRADE_STOP_LOSS=""
    TRADE_TAKE_PROFIT=""
    TRADE_API_KEY=""
    TRADE_API_SECRET=""
    TRADE_CREDENTIAL_PROFILE=""
    
    # Check if first argument is a symbol
    if [[ $# -gt 0 ]] && [[ ! "$1" =~ ^-- ]]; then
        TRADE_SYMBOL="$1"
        shift
    fi
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --symbol)
                TRADE_SYMBOL="$2"
                shift 2
                ;;
            --amount)
                TRADE_AMOUNT="$2"
                shift 2
                ;;
            --exchange)
                TRADE_EXCHANGE="$2"
                shift 2
                ;;
            --direction)
                TRADE_DIRECTION="$2"
                shift 2
                ;;
            --test-mode)
                TRADE_TEST_MODE="true"
                shift
                ;;
            --stop-loss)
                TRADE_STOP_LOSS="$2"
                shift 2
                ;;
            --take-profit)
                TRADE_TAKE_PROFIT="$2"
                shift 2
                ;;
            --key)
                TRADE_API_KEY="$2"
                export BYBIT_API_KEY="$2"
                shift 2
                ;;
            --secret)
                TRADE_API_SECRET="$2"
                export BYBIT_API_SECRET="$2"
                shift 2
                ;;
            --profile)
                TRADE_CREDENTIAL_PROFILE="$2"
                shift 2
                ;;
            *)
                shift
                ;;
        esac
    done
    
    # Normalize symbol format
    if [[ -n "$TRADE_SYMBOL" ]]; then
        if [[ "$TRADE_SYMBOL" != *"/"* ]]; then
            local input_symbol=$(echo "${TRADE_SYMBOL}" | tr '[:lower:]' '[:upper:]')

            # Handle different input formats
            if [[ "$input_symbol" == *"USDT" ]]; then
                # Input like "ETHUSDT" -> "ETH/USDT:USDT"
                local base_symbol="${input_symbol%USDT}"
                TRADE_SYMBOL="${base_symbol}/USDT:USDT"
            else
                # Input like "ETH" -> "ETH/USDT:USDT"
                TRADE_SYMBOL="${input_symbol}/USDT:USDT"
            fi
            echo "📝 Normalized symbol: $TRADE_SYMBOL"
        fi
    fi
}

# Update Docker images
update_docker_images() {
    local mode="${1:-both}"  # telegram, trader, or both

    case "$mode" in
        telegram)
            echo "🔄 Updating Telegram bot image: $TELEGRAM_IMAGE"
            if $DOCKER_CMD pull "$TELEGRAM_IMAGE"; then
                echo "✅ Telegram image updated successfully"
                send_telegram_notification "Telegram bot image updated: $TELEGRAM_IMAGE"
            else
                echo "⚠️ Failed to update Telegram image, using existing version"
            fi
            ;;
        trader)
            echo "🔄 Updating Trader bot image: $TRADER_IMAGE"
            if $DOCKER_CMD pull "$TRADER_IMAGE"; then
                echo "✅ Trader image updated successfully"
                send_telegram_notification "Trader bot image updated: $TRADER_IMAGE"
            else
                echo "⚠️ Failed to update Trader image, using existing version"
            fi
            ;;
        both|*)
            echo "🔄 Updating both Docker images..."
            update_docker_images telegram
            update_docker_images trader
            ;;
    esac
}

# Run Docker container
run_docker_container() {
    local detach_mode="${1:-true}"
    local container_name="${2:-crypto-trading-bot}"
    
    # Remove existing container if it exists
    $DOCKER_CMD stop "$container_name" 2>/dev/null || true
    $DOCKER_CMD rm "$container_name" 2>/dev/null || true

    # Build docker run command
    local docker_run_cmd="$DOCKER_CMD run"
    
    if [[ "$detach_mode" == "true" ]]; then
        docker_run_cmd+=" -d"
    fi

    docker_run_cmd+=" --name $container_name"
    docker_run_cmd+=" --restart unless-stopped"
    
    # Add environment variables
    [[ -n "$TRADE_SYMBOL" ]] && docker_run_cmd+=" -e TRADE_SYMBOL='$TRADE_SYMBOL'"
    [[ -n "$TRADE_AMOUNT" ]] && docker_run_cmd+=" -e TRADE_AMOUNT='$TRADE_AMOUNT'"
    [[ -n "$TRADE_EXCHANGE" ]] && docker_run_cmd+=" -e TRADE_EXCHANGE='$TRADE_EXCHANGE'"
    [[ -n "$TRADE_DIRECTION" ]] && docker_run_cmd+=" -e TRADE_DIRECTION='$TRADE_DIRECTION'"
    # Force live trading mode (not test mode)
    docker_run_cmd+=" -e TRADE_TEST_MODE='false'"
    docker_run_cmd+=" -e TEST_MODE='false'"
    [[ -n "$TRADE_STOP_LOSS" ]] && docker_run_cmd+=" -e TRADE_STOP_LOSS='$TRADE_STOP_LOSS'"
    [[ -n "$TRADE_TAKE_PROFIT" ]] && docker_run_cmd+=" -e TRADE_TAKE_PROFIT='$TRADE_TAKE_PROFIT'"
    [[ -n "$BYBIT_API_KEY" ]] && docker_run_cmd+=" -e BYBIT_API_KEY='$BYBIT_API_KEY'"
    [[ -n "$BYBIT_API_SECRET" ]] && docker_run_cmd+=" -e BYBIT_API_SECRET='$BYBIT_API_SECRET'"

    docker_run_cmd+=" $TRADER_IMAGE python3 main.py --start"
    
    echo "🚀 Starting container: $container_name"
    echo "🖼️  Image: $TRADER_IMAGE"
    echo "Command: $docker_run_cmd"

    if eval "$docker_run_cmd"; then
        echo "✅ Container started successfully: $container_name"
        send_telegram_notification "Trading bot started: $container_name" "start" 0 "Container: $container_name\\nSymbol: $TRADE_SYMBOL\\nAmount: $TRADE_AMOUNT"
        return 0
    else
        echo "❌ Failed to start container: $container_name"
        send_telegram_notification "Failed to start trading bot" "start" 1 "" "Container start failed: $container_name"
        return 1
    fi
}

# Load credentials from profile and export to environment
load_credentials_profile() {
    local profile="${1:-main}"
    local creds_file="$HOME/.traderbot/credentials/${profile}.env"

    echo "🔄 Loading credentials for profile: $profile"

    if [[ ! -f "$creds_file" ]]; then
        echo "❌ Profile not found: $profile"
        echo "💡 List profiles with /listcreds command"
        return 1
    fi

    # Source the credentials file to load into environment
    source "$creds_file"

    # Export credentials so they're available to child processes
    export BYBIT_API_KEY
    export BYBIT_API_SECRET

    # Get display name if available
    local display_file="$HOME/.traderbot/credentials/${profile}.display"
    local display_name="$profile"
    if [[ -f "$display_file" ]]; then
        display_name=$(cat "$display_file" 2>/dev/null || echo "$profile")
    fi

    echo "✅ Credentials loaded"
    echo "   Profile: $profile"
    echo "   Display Name: $display_name"
    echo "   API Key: ${BYBIT_API_KEY:0:8}..."

    return 0
}

# Start bot
start_bot() {
    echo "🤖 Starting Trading Bot"
    echo "======================="

    parse_trading_arguments "$@"

    if [[ -z "$TRADE_SYMBOL" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: $0 start <symbol> [options]"
        return 1
    fi

    if ! check_docker; then
        return 1
    fi

    CONTAINER_NAME=$(generate_container_name "$TRADE_SYMBOL")

    echo "📊 Trading Configuration:"
    echo "   Symbol: $TRADE_SYMBOL"
    echo "   Amount: ${TRADE_AMOUNT:-default}"
    echo "   Container: $CONTAINER_NAME"
    echo "   Test Mode: ${TRADE_TEST_MODE:-false}"

    # Load credentials - use specified profile or fallback to main
    echo "🔑 Loading credentials..."

    # Determine which profile to use
    local profile_to_use="${TRADE_CREDENTIAL_PROFILE:-main}"
    echo "🔍 Profile to use: $profile_to_use"

    if [[ -n "$BYBIT_API_KEY" && -n "$BYBIT_API_SECRET" && -z "$TRADE_CREDENTIAL_PROFILE" ]]; then
        # Credentials already loaded in environment and no specific profile requested
        echo "✅ Credentials already loaded in environment"
        echo "   API Key: ${BYBIT_API_KEY:0:8}..."
    else
        # Load from specified profile
        local creds_file="$HOME/.traderbot/credentials/${profile_to_use}.env"
        echo "🔍 Looking for credentials file: $creds_file"

        if [[ -f "$creds_file" ]]; then
            echo "📄 Found credentials file, loading..."
            # Source the credentials file to load into environment
            source "$creds_file"
            export BYBIT_API_KEY
            export BYBIT_API_SECRET
            echo "✅ Credentials loaded from profile: $profile_to_use"
            echo "   API Key: ${BYBIT_API_KEY:0:8}..."
            echo "   API Secret: ${BYBIT_API_SECRET:0:8}..."
        else
            echo "❌ Profile not found: $profile_to_use"
            echo "� Available profiles:"
            ls -la "$HOME/.traderbot/credentials/" 2>/dev/null || echo "   No credentials directory found"
            echo "�💡 Add credentials first with /addcreds command"
            return 1
        fi
    fi

    # Verify credentials are set before proceeding
    if [[ -z "$BYBIT_API_KEY" || -z "$BYBIT_API_SECRET" ]]; then
        echo "❌ Credentials not properly loaded!"
        echo "   BYBIT_API_KEY: ${BYBIT_API_KEY:+SET}"
        echo "   BYBIT_API_SECRET: ${BYBIT_API_SECRET:+SET}"
        return 1
    fi

    update_docker_images trader
    run_docker_container true "$CONTAINER_NAME"
}

# Stop bot
stop_bot() {
    local symbol="$1"
    
    if [[ -z "$symbol" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: $0 stop <symbol>"
        return 1
    fi
    
    local container_name=$(generate_container_name "$symbol")
    
    echo "⏹️ Stopping trading bot: $container_name"

    if $DOCKER_CMD stop "$container_name" 2>/dev/null; then
        echo "✅ Container stopped: $container_name"
        send_telegram_notification "Trading bot stopped: $container_name"
    else
        echo "⚠️ Container not running: $container_name"
    fi

    if $DOCKER_CMD rm "$container_name" 2>/dev/null; then
        echo "✅ Container removed: $container_name"
    fi
}

# Restart bot
restart_bot() {
    local symbol="$1"
    
    if [[ -z "$symbol" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: $0 restart <symbol>"
        return 1
    fi
    
    echo "🔄 Restarting bot for symbol: $symbol"
    
    stop_bot "$symbol"
    sleep 2
    
    # Load previous config if available
    local container_name=$(generate_container_name "$symbol")
    local config_file="$HOME/.traderbot/configs/${container_name}.env"
    
    if [[ -f "$config_file" ]]; then
        echo "📋 Loading previous configuration..."
        source "$config_file"
    fi
    
    start_bot "$symbol" --amount "${TRADE_AMOUNT:-50}"
}

# List containers
list_containers() {
    echo "📊 Trading Bot Containers"
    echo "========================="

    if ! check_docker; then
        return 1
    fi

    # Get all containers, excluding system containers
    local all_containers=$($DOCKER_CMD ps -a --format "{{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}" 2>/dev/null)

    if [[ -z "$all_containers" ]]; then
        echo "❌ No containers found"
        return 1
    fi

    # Filter for trading bot containers (exclude telegram and system containers)
    local trading_containers=$(echo "$all_containers" | grep -v -E "(autotrader-telegram|postgres|redis|nginx)" | grep -E "^[a-z]+[[:space:]]" || true)

    if [[ -z "$trading_containers" ]]; then
        echo "📭 No trading bot containers found"
        echo ""
        echo "💡 Use '/createbot' to create a new trading bot"
        return 1
    fi

    # Format output with headers
    echo "NAME                STATUS              IMAGE                           CREATED"
    echo "--------------------------------------------------------------------------------"
    echo "$trading_containers"

    # Count containers by status
    local running_count=$(echo "$trading_containers" | grep -c "Up " || echo "0")
    local stopped_count=$(echo "$trading_containers" | grep -c "Exited " || echo "0")
    local total_count=$(echo "$trading_containers" | wc -l)

    echo ""
    echo "📈 Summary: $total_count total, $running_count running, $stopped_count stopped"

    return 0
}

# Get bot status
get_status() {
    local symbol="$1"

    if [[ -z "$symbol" ]]; then
        list_containers
        return 0
    fi

    # Find container using enhanced search
    local container_name=$(find_container_by_symbol "$symbol")

    if [[ -z "$container_name" ]]; then
        echo "❌ No container found for symbol: $symbol"
        echo ""
        echo "💡 Available containers:"
        list_containers
        return 1
    fi

    echo "📊 Status for: $symbol (container: $container_name)"
    echo "=================================================="

    # Show detailed container information
    $DOCKER_CMD ps -a --filter name="$container_name" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}"

    # Show additional details if container is running
    if $DOCKER_CMD ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        echo ""
        echo "📈 Runtime Information:"
        echo "======================"

        # Get container stats
        local stats=$($DOCKER_CMD stats --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" "$container_name" 2>/dev/null)
        if [[ -n "$stats" ]]; then
            echo "$stats"
        fi

        # Show recent logs (last 5 lines)
        echo ""
        echo "📋 Recent Logs (last 5 lines):"
        echo "==============================="
        $DOCKER_CMD logs --tail 5 "$container_name" 2>&1 | sed 's/^/   /'
    fi
}

# Get bot logs
get_logs() {
    local symbol="$1"
    local lines="${2:-50}"

    if [[ -z "$symbol" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: $0 logs <symbol> [lines]"
        return 1
    fi

    # Find container using enhanced search
    local container_name=$(find_container_by_symbol "$symbol")

    if [[ -z "$container_name" ]]; then
        echo "❌ No container found for symbol: $symbol"
        echo ""
        echo "💡 Available containers:"
        list_containers
        return 1
    fi

    echo "📋 Logs for $symbol (container: $container_name, last $lines lines):"
    echo "=================================================================="

    # Check if container exists
    if ! $DOCKER_CMD ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
        echo "❌ Container $container_name not found"
        return 1
    fi

    # Get logs with timestamps
    $DOCKER_CMD logs --tail "$lines" --timestamps "$container_name" 2>&1
}

# Start Telegram bot using simple curl-based approach
start_telegram_bot() {
    echo "🤖 Starting Central Trading Bot Manager (Telegram Bot)"
    echo "======================================================"

    # Check environment variables
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "❌ TELEGRAM_BOT_TOKEN environment variable is required"
        echo ""
        echo "Please set your bot token:"
        echo "  export TELEGRAM_BOT_TOKEN='your_bot_token_here'"
        echo ""
        echo "📖 See TELEGRAM_SETUP.md for detailed setup instructions"
        return 1
    fi

    if [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        echo "❌ TELEGRAM_CHAT_ID environment variable is required"
        echo ""
        echo "Please set your chat ID:"
        echo "  export TELEGRAM_CHAT_ID='your_chat_id_here'"
        echo ""
        echo "� Get your chat ID by messaging @userinfobot on Telegram"
        echo "📖 See TELEGRAM_SETUP.md for detailed setup instructions"
        return 1
    fi

    # Check dependencies
    if ! command -v curl &> /dev/null; then
        echo "❌ curl is required for Telegram bot"
        echo "Please install curl and try again"
        return 1
    fi

    if ! command -v jq &> /dev/null; then
        echo "❌ jq is required for JSON processing"
        echo "Please install jq and try again"
        return 1
    fi

    echo "✅ Bot Token: ${TELEGRAM_BOT_TOKEN:0:10}...${TELEGRAM_BOT_TOKEN: -4}"
    echo "✅ Chat ID: $TELEGRAM_CHAT_ID"
    echo ""

    # Test API connection
    echo "🔍 Testing Telegram API connection..."
    local me_response=$(curl -s "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/getMe")

    if echo "$me_response" | jq -e '.ok' >/dev/null 2>&1; then
        local bot_username=$(echo "$me_response" | jq -r '.result.username')
        local bot_name=$(echo "$me_response" | jq -r '.result.first_name')
        echo "✅ Connected to Telegram Bot: @$bot_username ($bot_name)"
    else
        echo "❌ Failed to connect to Telegram API"
        echo "Response: $me_response"
        return 1
    fi

    # Send startup message
    local startup_msg="🎛️ **Central Trading Bot Manager Started**\n\n"
    startup_msg+="✅ **System Ready:**\n"
    startup_msg+="• 🔑 Credential Management\n"
    startup_msg+="• 🤖 Bot Operations\n"
    startup_msg+="• 🐳 Docker Integration\n"
    startup_msg+="• 💬 Interactive Commands\n\n"
    startup_msg+="💡 Type /start to begin or /help for all commands!"

    telegram_send_message "$TELEGRAM_CHAT_ID" "$startup_msg"

    echo "🚀 Telegram Bot started successfully!"
    echo "💬 Bot is now listening for messages..."
    echo "📱 Go to Telegram and type /start to interact with your bot"
    echo ""
    echo "🔧 Available commands:"
    echo "  /start     - Welcome message with quick actions"
    echo "  /help      - Show all available commands"
    echo "  /list      - List all trading containers"
    echo "  /status    - System status information"
    echo ""
    echo "⏹️  Press Ctrl+C to stop the bot"
    echo ""

    # Simple message: Bot is running in basic mode
    echo "📋 Running in basic notification mode"
    echo "💡 For full interactive features, use the Python-based bot"
    echo ""
    echo "🔄 Bot will send notifications for system events"
    echo "✅ Bot is ready and running!"
}

# Deploy to server mode
deploy_server() {
    echo "🚀 Deploying AutoTrader to Server Mode"
    echo "======================================"
    
    local mode="${1:-both}"  # telegram, trader, or both
    
    case "$mode" in
        telegram)
            deploy_telegram_bot
            ;;
        trader)
            deploy_trader_bot
            ;;
        both|*)
            deploy_telegram_bot
            deploy_trader_bot
            ;;
    esac
}

# Deploy telegram bot with enhanced error handling
deploy_telegram_bot() {
    echo "📱 Deploying Telegram Bot"
    echo "========================="

    if ! check_docker; then
        echo "❌ Docker check failed"
        return 1
    fi

    # Check required environment variables
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "❌ TELEGRAM_BOT_TOKEN not set"
        echo "💡 Set it in your environment or .env file"
        return 1
    fi

    if [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        echo "❌ TELEGRAM_CHAT_ID not set"
        echo "💡 Set it in your environment or .env file"
        return 1
    fi

    # Pull latest telegram bot image
    echo "📥 Pulling latest Telegram bot image..."
    if ! update_docker_images telegram; then
        echo "⚠️ Failed to update image, using existing version"
    fi

    # Stop existing telegram bot
    echo "🛑 Stopping existing Telegram bot..."
    $DOCKER_CMD stop autotrader-telegram 2>/dev/null || true
    $DOCKER_CMD rm autotrader-telegram 2>/dev/null || true

    # Start telegram bot container with production image
    echo "🚀 Starting Telegram bot container..."
    if $DOCKER_CMD run -d \
        --name autotrader-telegram \
        --restart unless-stopped \
        -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
        -e TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID" \
        -v /var/run/docker.sock:/var/run/docker.sock:ro \
        "$TELEGRAM_IMAGE"; then

        # Wait a moment and check if container is running
        sleep 3
        if $DOCKER_CMD ps --format "{{.Names}}" | grep -q "^autotrader-telegram$"; then
            echo "✅ Telegram bot deployed and running successfully"
            echo "📱 Container: autotrader-telegram"
            echo "🖼️  Image: $TELEGRAM_IMAGE"
            echo "🔍 Status: $(docker ps --format "{{.Status}}" --filter name=autotrader-telegram)"
            return 0
        else
            echo "❌ Telegram bot container failed to start"
            echo "📋 Checking logs..."
            $DOCKER_CMD logs autotrader-telegram 2>/dev/null | tail -10
            return 1
        fi
    else
        echo "❌ Failed to start Telegram bot container"
        return 1
    fi
}

# Deploy trader bot infrastructure with enhanced validation
deploy_trader_bot() {
    echo "🤖 Deploying Trader Bot Infrastructure"
    echo "====================================="

    if ! check_docker; then
        echo "❌ Docker check failed"
        return 1
    fi

    # Pull latest trader image
    echo "📥 Pulling latest trader image..."
    if update_docker_images trader; then
        echo "✅ Trader image updated successfully"
    else
        echo "⚠️ Failed to update trader image, using existing version"
        # Check if image exists
        if ! $DOCKER_CMD images --format "{{.Repository}}:{{.Tag}}" | grep -q "^${TRADER_IMAGE}$"; then
            echo "❌ Trader image not found: $TRADER_IMAGE"
            echo "💡 Please check your image configuration"
            return 1
        fi
    fi

    # Verify trader image is functional
    echo "🔍 Verifying trader image functionality..."
    if $DOCKER_CMD run --rm "$TRADER_IMAGE" python3 -c "import sys; print('Python OK'); sys.exit(0)" >/dev/null 2>&1; then
        echo "✅ Trader image is functional"
    else
        echo "⚠️ Trader image may have issues, but continuing..."
    fi

    echo "✅ Trader bot infrastructure ready"
    echo "🖼️  Image: $TRADER_IMAGE"
    echo "💡 Use Telegram commands or './bot.sh start <symbol>' to create trading bots"
    echo "💡 Check available commands with './bot.sh help'"

    return 0
}

# Start both telegram and trader systems with comprehensive monitoring
start_all() {
    echo "🚀 Starting Complete AutoTrader System"
    echo "======================================"

    local telegram_success=false
    local trader_success=false
    local overall_success=true

    # Start telegram bot in background
    echo ""
    echo "📱 Starting Telegram bot..."
    echo "----------------------------"
    if deploy_telegram_bot; then
        telegram_success=true
        echo "✅ Telegram bot started successfully"
    else
        echo "❌ Telegram bot failed to start"
        overall_success=false
    fi

    echo ""
    echo "🤖 Preparing trader infrastructure..."
    echo "------------------------------------"
    if deploy_trader_bot; then
        trader_success=true
        echo "✅ Trader infrastructure ready"
    else
        echo "❌ Trader infrastructure setup failed"
        overall_success=false
    fi

    echo ""
    echo "📊 System Status Summary"
    echo "========================"
    echo "📱 Telegram Bot: $([ "$telegram_success" = true ] && echo "✅ Running" || echo "❌ Failed")"
    echo "🤖 Trader Infrastructure: $([ "$trader_success" = true ] && echo "✅ Ready" || echo "❌ Failed")"

    if [ "$overall_success" = true ]; then
        echo ""
        echo "🎉 AutoTrader system started successfully!"
        echo ""
        echo "📋 Next steps:"
        echo "  • Use Telegram commands to manage trading bots"
        echo "  • Or use './bot.sh start <symbol>' for manual control"
        echo "  • Check status with './bot.sh system-status'"
        echo "  • View logs with './bot.sh logs <container_name>'"

        # Send success notification
        send_telegram_notification "🎉 AutoTrader system started successfully! Ready for trading operations."

        return 0
    else
        echo ""
        echo "⚠️ AutoTrader system started with some issues"
        echo "💡 Check the errors above and fix them"
        echo "💡 Use './bot.sh system-status' to check current status"

        # Send warning notification
        send_telegram_notification "⚠️ AutoTrader system started with issues. Check logs for details."

        return 1
    fi
}

# Check service health with detailed status
check_service_health() {
    local service_name="$1"
    local container_name="$2"

    echo "🔍 Checking $service_name health..."

    # Check if container exists and is running
    if $DOCKER_CMD ps --format "{{.Names}}" 2>/dev/null | grep -q "^${container_name}$"; then
        local status=$($DOCKER_CMD ps --format "{{.Status}}" --filter name="$container_name" 2>/dev/null)
        local uptime=$(echo "$status" | grep -o "Up [^,]*" || echo "Unknown")

        echo "✅ $service_name: Running ($uptime)"

        # Check container logs for recent errors
        local recent_errors=$($DOCKER_CMD logs --tail 10 "$container_name" 2>&1 | grep -i "error\|exception\|failed" | wc -l)
        if [ "$recent_errors" -gt 0 ]; then
            echo "⚠️  Recent errors found in logs ($recent_errors)"
        fi

        return 0
    else
        echo "❌ $service_name: Not running"

        # Check if container exists but stopped
        if $DOCKER_CMD ps -a --format "{{.Names}}" 2>/dev/null | grep -q "^${container_name}$"; then
            local exit_code=$($DOCKER_CMD ps -a --format "{{.Status}}" --filter name="$container_name" 2>/dev/null | grep -o "Exited ([0-9]*)" | grep -o "[0-9]*")
            echo "💡 Container exists but stopped (exit code: ${exit_code:-unknown})"

            # Show last few log lines
            echo "📋 Last logs:"
            $DOCKER_CMD logs --tail 5 "$container_name" 2>&1 | sed 's/^/   /'
        else
            echo "💡 Container does not exist"
        fi

        return 1
    fi
}

# Enhanced system status with health checks
enhanced_system_status() {
    echo "🔍 Enhanced System Status Check"
    echo "==============================="

    local telegram_healthy=false
    local trader_ready=false

    # Check Telegram bot health
    if check_service_health "Telegram Bot" "autotrader-telegram"; then
        telegram_healthy=true
    fi

    echo ""

    # Check trader infrastructure
    echo "🔍 Checking Trader Infrastructure..."
    if $DOCKER_CMD images --format "{{.Repository}}:{{.Tag}}" | grep -q "^${TRADER_IMAGE}$"; then
        echo "✅ Trader Infrastructure: Image available ($TRADER_IMAGE)"
        trader_ready=true
    else
        echo "❌ Trader Infrastructure: Image not found ($TRADER_IMAGE)"
    fi

    echo ""
    echo "🤖 Active Trading Bots:"
    local trading_containers=$($DOCKER_CMD ps --format "table {{.Names}}\t{{.Status}}\t{{.Image}}" 2>/dev/null | grep -E "(crypto|trading)" | grep -v "autotrader-telegram" || true)

    if [[ -n "$trading_containers" ]]; then
        echo "$trading_containers"
    else
        echo "   No active trading bots"
    fi

    echo ""
    echo "📊 Overall System Health:"
    echo "========================="
    echo "📱 Telegram Bot: $([ "$telegram_healthy" = true ] && echo "✅ Healthy" || echo "❌ Unhealthy")"
    echo "🤖 Trader Infrastructure: $([ "$trader_ready" = true ] && echo "✅ Ready" || echo "❌ Not Ready")"

    if [ "$telegram_healthy" = true ] && [ "$trader_ready" = true ]; then
        echo ""
        echo "🎉 System is healthy and ready for trading!"
        return 0
    else
        echo ""
        echo "⚠️ System has issues that need attention"
        echo "💡 Use './bot.sh start-all' to fix common issues"
        return 1
    fi
}

# Stop all services
stop_all() {
    echo "⏹️ Stopping AutoTrader System"
    echo "============================="
    
    # Stop telegram bot
    echo "📱 Stopping Telegram bot..."
    $DOCKER_CMD stop autotrader-telegram 2>/dev/null || true
    $DOCKER_CMD rm autotrader-telegram 2>/dev/null || true

    # Stop all trading containers
    echo "🤖 Stopping all trading bots..."
    local containers=$($DOCKER_CMD ps --format "{{.Names}}" | grep -E "(crypto|trading|autotrader)" || true)
    
    if [[ -n "$containers" ]]; then
        echo "$containers" | while read container; do
            echo "  Stopping: $container"
            $DOCKER_CMD stop "$container" 2>/dev/null || true
            $DOCKER_CMD rm "$container" 2>/dev/null || true
        done
    fi
    
    echo "✅ All services stopped"
    send_telegram_notification "⏹️ AutoTrader system stopped. All trading operations halted."
}

# System status overview
system_status() {
    echo "📊 AutoTrader System Status"
    echo "=========================="

    # Initialize Docker command
    check_docker >/dev/null 2>&1 || true

    # Check telegram bot
    echo "📱 Telegram Bot:"
    if $DOCKER_CMD ps --format "{{.Names}}" 2>/dev/null | grep -q "autotrader-telegram"; then
        echo "   Status: ✅ Running (Docker)"
    elif ps aux 2>/dev/null | grep -q "[r]un_telegram_app.py"; then
        echo "   Status: ✅ Running (Local)"
    else
        echo "   Status: ❌ Stopped"
    fi

    # Check trading bots
    echo ""
    echo "🤖 Trading Bots:"
    local trading_containers=$($DOCKER_CMD ps --format "table {{.Names}}\t{{.Status}}" 2>/dev/null | grep -E "(crypto|trading|autotrader)" | grep -v "autotrader-telegram" || true)
    
    if [[ -n "$trading_containers" ]]; then
        echo "$trading_containers"
    else
        echo "   No active trading bots"
    fi
    
    # Check Docker
    echo ""
    echo "🐳 Docker Status:"
    if check_docker; then
        echo "   Status: ✅ Available"
        local telegram_images=$($DOCKER_CMD images --format "{{.Repository}}:{{.Tag}}" | grep "$IMAGE_BASE-telegram" | wc -l | tr -d ' ')
        local trader_images=$($DOCKER_CMD images --format "{{.Repository}}:{{.Tag}}" | grep "$IMAGE_BASE-trader" | wc -l | tr -d ' ')
        echo "   Telegram images: $telegram_images"
        echo "   Trader images: $trader_images"
        echo "   📱 Telegram: $TELEGRAM_IMAGE"
        echo "   🤖 Trader: $TRADER_IMAGE"
    else
        echo "   Status: ❌ Not available"
    fi
    
    # Check environment
    echo ""
    echo "🔧 Environment:"
    if [[ -n "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "   Telegram Token: ✅ Set (${TELEGRAM_BOT_TOKEN:0:10}...)"
    else
        echo "   Telegram Token: ❌ Not set"
    fi
    
    if [[ -n "$TELEGRAM_CHAT_ID" ]]; then
        echo "   Chat ID: ✅ Set ($TELEGRAM_CHAT_ID)"
    else
        echo "   Chat ID: ❌ Not set"
    fi
    
    echo "   Python venv: $([[ -f ".venv/bin/activate" ]] && echo "✅ Available" || echo "❌ Not found")"
}

# Stop all trading containers
stop_all_containers() {
    echo "⏹️ Stopping All Trading Containers"
    echo "=================================="

    # Get all autotrader containers except telegram
    local containers=$($DOCKER_CMD ps --format "{{.Names}}" | grep -E "(autotrader|trading|crypto)" | grep -v "autotrader-telegram" || true)

    if [[ -z "$containers" ]]; then
        echo "📭 No trading containers found"
        return 0
    fi

    echo "🔍 Found containers:"
    echo "$containers"
    echo ""

    # Stop each container
    local stopped_count=0
    while IFS= read -r container; do
        if [[ -n "$container" ]]; then
            echo "⏹️ Stopping: $container"
            if $DOCKER_CMD stop "$container" >/dev/null 2>&1; then
                echo "   ✅ Stopped successfully"
                ((stopped_count++))
            else
                echo "   ❌ Failed to stop"
            fi
        fi
    done <<< "$containers"

    echo ""
    echo "✅ Stopped $stopped_count containers"
}

# List configuration files
list_config_files() {
    echo "📁 Configuration Files"
    echo "======================"

    local configs_dir="configs"

    if [[ ! -d "$configs_dir" ]]; then
        echo "❌ Configs directory not found: $configs_dir"
        echo "💡 Create configs directory and add .json files"
        return 1
    fi

    # Find all .json files
    local config_files=$(find "$configs_dir" -name "*.json" -type f 2>/dev/null || true)

    if [[ -z "$config_files" ]]; then
        echo "📭 No configuration files found"
        echo "💡 Add .json config files to $configs_dir directory"
        return 0
    fi

    echo "✅ Found configuration files:"
    echo ""

    while IFS= read -r config_file; do
        if [[ -n "$config_file" ]]; then
            local filename=$(basename "$config_file")
            local size=$(stat -f%z "$config_file" 2>/dev/null || stat -c%s "$config_file" 2>/dev/null || echo "unknown")
            local modified=$(stat -f%Sm "$config_file" 2>/dev/null || stat -c%y "$config_file" 2>/dev/null | cut -d' ' -f1 || echo "unknown")

            echo "  📄 $filename"
            echo "     Size: $size bytes"
            echo "     Modified: $modified"
            echo ""
        fi
    done <<< "$config_files"
}

# Show configuration file content
show_config_file() {
    local config_name="$1"

    if [[ -z "$config_name" ]]; then
        echo "❌ Usage: show-config <config_name>"
        return 1
    fi

    local config_file="configs/${config_name}.json"

    if [[ ! -f "$config_file" ]]; then
        echo "❌ Configuration file not found: $config_file"
        echo "💡 Use 'list-configs' to see available configurations"
        return 1
    fi

    echo "📄 Configuration: $config_name"
    echo "================================"
    echo ""

    # Show file info
    local size=$(stat -f%z "$config_file" 2>/dev/null || stat -c%s "$config_file" 2>/dev/null || echo "unknown")
    local modified=$(stat -f%Sm "$config_file" 2>/dev/null || stat -c%y "$config_file" 2>/dev/null | cut -d' ' -f1 || echo "unknown")

    echo "📁 File: $config_file"
    echo "📏 Size: $size bytes"
    echo "📅 Modified: $modified"
    echo ""
    echo "📄 Content:"
    echo "----------"

    # Pretty print JSON if possible
    if command -v jq >/dev/null 2>&1; then
        jq '.' "$config_file" 2>/dev/null || cat "$config_file"
    else
        cat "$config_file"
    fi
}

# Comprehensive setup for server deployment
setup_environment() {
    echo "🔧 Setting up Trading Bot Environment..."
    echo "========================================"

    # Detect OS
    OS="unknown"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        OS="windows"
    fi

    echo "🖥️  Detected OS: $OS"
    echo ""

    # Docker installation check/setup
    if command -v docker &> /dev/null; then
        echo "✅ Docker is already installed"
        docker --version
    else
        echo "❌ Docker is not installed"

        case "$OS" in
            "linux")
                echo "� Installing Docker on Linux..."

                # Update package index
                echo "📦 Updating package index..."
                sudo apt-get update

                # Install required packages
                echo "📦 Installing required packages..."
                sudo apt-get install -y \
                    ca-certificates \
                    curl \
                    gnupg \
                    lsb-release

                # Add Docker's official GPG key
                echo "🔑 Adding Docker GPG key..."
                sudo mkdir -p /etc/apt/keyrings
                curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

                # Set up repository
                echo "📋 Setting up Docker repository..."
                echo \
                    "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
                    $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

                # Install Docker Engine
                echo "🐳 Installing Docker Engine..."
                sudo apt-get update
                sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

                # Add user to docker group
                echo "👤 Adding user to docker group..."
                sudo usermod -aG docker $USER

                echo "✅ Docker installed successfully!"
                echo "⚠️  Please log out and log back in for group changes to take effect"
                ;;
            "macos")
                echo "🍎 macOS detected"
                echo "❗ Please install Docker Desktop manually:"
                echo "   1. Go to: https://www.docker.com/products/docker-desktop/"
                echo "   2. Download Docker Desktop for Mac"
                echo "   3. Install and start Docker Desktop"
                echo "   4. Run this setup command again after installation"
                echo ""
                echo "⏸️  Setup paused. Please install Docker Desktop first."
                exit 1
                ;;
            "windows")
                echo "🪟 Windows detected"
                echo "❗ Please install Docker Desktop manually:"
                echo "   1. Go to: https://www.docker.com/products/docker-desktop/"
                echo "   2. Download Docker Desktop for Windows"
                echo "   3. Install and start Docker Desktop"
                echo "   4. Run this setup command again after installation"
                echo ""
                echo "⏸️  Setup paused. Please install Docker Desktop first."
                exit 1
                ;;
            *)
                echo "❌ Unsupported OS: $OS"
                echo "Please install Docker manually and run setup again."
                exit 1
                ;;
        esac
    fi

    echo ""
    echo "📁 Creating required directories..."

    # Create directories
    mkdir -p ./configs
    mkdir -p ./data
    mkdir -p ./logs

    # Set proper permissions for Docker container
    chmod 777 ./configs ./data ./logs 2>/dev/null || true

    echo "✅ Created ./configs directory"
    echo "✅ Created ./data directory"
    echo "✅ Created ./logs directory"
    echo "🔧 Set proper permissions for Docker container"

    # Setup Python environment
    echo ""
    echo "🐍 Setting up Python environment..."

    # Check Python3
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 is required but not installed"
        case "$OS" in
            "linux")
                echo "📦 Installing Python3..."
                sudo apt-get update
                sudo apt-get install -y python3 python3-pip python3-venv
                ;;
            *)
                echo "Please install Python3 manually and run setup again"
                exit 1
                ;;
        esac
    fi

    # Setup virtual environment
    if [[ ! -f ".venv/bin/activate" ]]; then
        echo "� Creating Python virtual environment..."
        python3 -m venv .venv
        echo "✅ Virtual environment created"
    fi

    # Activate and install dependencies
    echo "📦 Installing Python dependencies..."
    source .venv/bin/activate

    # Install basic dependencies
    pip install --upgrade pip

    # Install Telegram dependencies if requirements exist
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
        echo "✅ Installed dependencies from requirements.txt"
    else
        # Install minimal dependencies for Telegram bot
        pip install python-telegram-bot requests
        echo "✅ Installed basic Telegram bot dependencies"
    fi

    echo ""
    echo "🎯 Setup Summary"
    echo "==============="
    echo "✅ Docker: $(docker --version 2>/dev/null || echo 'Not available')"
    echo "✅ Python: $(python3 --version 2>/dev/null || echo 'Not available')"
    echo "✅ Virtual Environment: $([[ -f ".venv/bin/activate" ]] && echo "Ready" || echo "Not created")"
    echo "✅ Directories: configs/, data/, logs/"
    echo ""
    echo "🚀 Setup completed successfully!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Set your Telegram credentials:"
    echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
    echo "   export TELEGRAM_CHAT_ID='your_chat_id'"
    echo ""
    echo "2. Start the system:"
    echo "   ./bot.sh start-all"
    echo ""
    echo "3. Or start individual components:"
    echo "   ./bot.sh telegram        # Start Telegram bot"
    echo "   ./bot.sh start <symbol>  # Start trading bot"
}

# Upgrade bot.sh script
upgrade_script() {
    echo "� Upgrading bot.sh from GitHub Gist..."
    echo "========================================"

    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        echo "❌ curl is required for upgrade. Please install curl first."
        exit 1
    fi

    # GitHub Gist URL for bot.sh updates
    GIST_RAW_URL=${BOT_GIST_URL:-"https://gist.githubusercontent.com/hoangtrung99/73593690940ff91015063f2b6f9366a3/raw/autotrader.sh"}

    # Get the current script path
    SCRIPT_PATH="$(realpath "${BASH_SOURCE[0]}")"
    BACKUP_PATH="${SCRIPT_PATH}.backup.$(date +%Y%m%d-%H%M%S)"

    echo "📄 Current script: $SCRIPT_PATH"
    echo "💾 Backup will be saved to: $BACKUP_PATH"
    echo "🌐 Gist URL: $GIST_RAW_URL"

    # Check if GIST_RAW_URL is accessible
    echo "🔍 Checking gist accessibility..."
    if ! curl -fsSL --head "$GIST_RAW_URL" > /dev/null; then
        echo ""
        echo "❌ Cannot access gist URL: $GIST_RAW_URL"
        echo "📝 Please check:"
        echo "   1. Your internet connection"
        echo "   2. The gist is public and accessible"
        echo "   3. The gist URL is correct"
        echo ""
        echo "💡 You can override the URL with:"
        echo "   export BOT_GIST_URL='your_custom_gist_raw_url'"
        exit 1
    fi

    # Create backup of current script
    echo ""
    echo "💾 Creating backup..."
    if cp "$SCRIPT_PATH" "$BACKUP_PATH"; then
        echo "✅ Backup created: $BACKUP_PATH"
    else
        echo "❌ Failed to create backup. Aborting upgrade."
        exit 1
    fi

    # Download new version to temporary file
    echo ""
    echo "📥 Downloading latest version..."
    TEMP_FILE=$(mktemp)

    if curl -fsSL "$GIST_RAW_URL" -o "$TEMP_FILE"; then
        echo "✅ Downloaded successfully"
    else
        echo "❌ Failed to download from gist. Check your internet connection and gist URL."
        echo "🔄 Restoring from backup..."
        rm -f "$TEMP_FILE"
        exit 1
    fi

    # Validate downloaded file (basic check)
    if [[ -s "$TEMP_FILE" ]] && head -1 "$TEMP_FILE" | grep -q "#!/bin/bash"; then
        echo "✅ Downloaded file appears valid"
    else
        echo "❌ Downloaded file appears invalid or empty"
        echo "🔄 Keeping current version"
        rm -f "$TEMP_FILE"
        exit 1
    fi

    # Check if there are actual changes
    if cmp -s "$SCRIPT_PATH" "$TEMP_FILE"; then
        echo "✅ Already up to date - no changes needed"
        rm -f "$TEMP_FILE"
        exit 0
    fi

    # Replace current script with new version
    echo ""
    echo "🔄 Installing new version..."
    if cp "$TEMP_FILE" "$SCRIPT_PATH" && chmod +x "$SCRIPT_PATH"; then
        echo "✅ Upgrade completed successfully!"
        echo ""
        echo "🎉 bot.sh has been upgraded to the latest version"
        echo ""
        echo "🔍 To verify the upgrade worked:"
        echo "   ./bot.sh version"
        echo ""
        echo "🗑️  To cleanup backup files:"
        echo "   rm $BACKUP_PATH"

    else
        echo "❌ Failed to install new version"
        echo "🔄 Restoring from backup..."
        if cp "$BACKUP_PATH" "$SCRIPT_PATH"; then
            echo "✅ Restored from backup"
        else
            echo "❌ Failed to restore backup! Manual intervention required."
            echo "💾 Backup file: $BACKUP_PATH"
        fi
        rm -f "$TEMP_FILE"
        exit 1
    fi

    rm -f "$TEMP_FILE"
}

# Quick setup function
quick_setup() {
    echo "🐳 Pulling Docker images..."
    update_docker_images both

    # Check telegram setup
    echo "📱 Checking Telegram setup..."
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]] || [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        echo "⚠️ Telegram not configured. Set environment variables:"
        echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
        echo "   export TELEGRAM_CHAT_ID='your_chat_id'"
    else
        echo "✅ Telegram configured"
    fi

    echo ""
    echo "🎉 Quick setup completed!"
    echo ""
    echo "📋 Next steps:"
    echo "  1. Configure Telegram (if not done): export TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID"
    echo "  2. Start system: './bot.sh start-all'"
    echo "  3. Or start individual components:"
    echo "     • './bot.sh telegram' - Start Telegram bot only"
    echo "     • './bot.sh start <symbol>' - Start trading bot only"
}

# Show help
show_help() {
    cat << EOF
🤖 AutoTrader Bot - Complete Trading System Controller

USAGE:
    $0 <command> [options]

📋 DEPLOYMENT COMMANDS (Server Setup):
    setup                      Complete environment setup (Docker, Python, dependencies)
    upgrade                    Upgrade bot.sh script from GitHub Gist
    start-all                  Start complete AutoTrader system (Telegram + Trader)
    stop-all                   Stop all AutoTrader services
    system-status             Show complete system status
    deploy <mode>             Deploy to server mode (telegram|trader|both)

📱 TELEGRAM BOT COMMANDS:
    telegram                  Start Telegram bot (curl-based, minimal dependencies)
    telegram-deploy           Deploy Telegram bot in Docker container

🚀 TRADING BOT COMMANDS:
    start <symbol> [options]  Create and start new trading bot

    Examples:
      $0 start eth --amount 100 --test-mode
      $0 start btc --amount 50 --direction long --stop-loss 5

📊 INDIVIDUAL BOT MANAGEMENT (Use Telegram Interface):

    Telegram Commands (Recommended):
    /list                     List all trading containers
    /status <symbol>          Check bot status (e.g. /status eth)
    /logs <symbol> [lines]    View bot logs
    /stop <symbol>            Stop trading bot
    /restart <symbol>         Restart trading bot
    /remove <symbol>          Remove bot container

    Legacy Commands (Deprecated):
    stop <symbol>             Stop trading bot (shows migration warning)
    restart <symbol>          Restart trading bot (shows migration warning)
    list                      List containers (shows migration warning)
    status <symbol>           Show status (shows migration warning)
    logs <symbol>             Show logs (shows migration warning)

CREDENTIAL COMMANDS:
    list-credentials         List stored API credentials
    store-credentials        Store API credentials securely
    load-credentials         Load credentials into environment
    show-credentials         Show credential details (masked)

CONFIG COMMANDS:
    list-configs             List configuration files
    show-config <name>       Show configuration content

UTILITY COMMANDS:
    help                     Show this help
    version                  Show version information

START OPTIONS:
    --amount <amount>        Trading amount (default: 50)
    --test-mode             Enable test mode
    --direction <long|short> Trading direction
    --stop-loss <percent>    Stop loss percentage
    --take-profit <percent>  Take profit percentage
    --key <api_key>         API key (or use env BYBIT_API_KEY)
    --secret <api_secret>   API secret (or use env BYBIT_API_SECRET)

EXAMPLES:
    # Quick setup new installation
    $0 setup

    # Start complete system (recommended)
    $0 start-all                  # Start both Telegram & Trader bots
    $0 deploy-all                 # Alternative command (same as start-all)

    # Individual components
    $0 telegram                   # Start Telegram bot only
    $0 telegram-deploy            # Deploy Telegram bot with Docker
    $0 start hyper --amount 50    # Start HYPER trading bot

    # System management
    $0 system-status              # Check all services status
    $0 stop-all                   # Stop everything safely
    $0 list                       # List running containers

    # Server deployment (advanced)
    $0 deploy both                # Deploy both services
    $0 deploy telegram            # Deploy only Telegram bot
    $0 deploy trader              # Deploy only Trader infrastructure

TELEGRAM SETUP:
    export TELEGRAM_BOT_TOKEN="your_bot_token"
    export TELEGRAM_CHAT_ID="your_chat_id"

FEATURES:
    ✅ Complete system control via single bot.sh file
    ✅ Manual trading bot management on server
    ✅ Full remote control via Telegram
    ✅ Docker deployment for production
    ✅ Unified monitoring and status reporting

For detailed documentation, visit: https://github.com/your-repo/autotrader

EOF
}

# Show version
show_version() {
    echo "🤖 AutoTrader Bot System"
    echo "======================="
    echo "Version: $SCRIPT_VERSION"
    echo "Build: Production Ready"
    echo ""
    echo "Components:"
    echo "  • Trading Engine: Docker-based"
    echo "  • Telegram Bot: Python-based"
    echo "  • CLI Interface: Bash-based"
    echo ""
    echo "Requirements:"
    if check_docker >/dev/null 2>&1; then
        echo "  • Docker: $($DOCKER_CMD --version 2>/dev/null)"
    else
        echo "  • Docker: Not installed"
    fi
    echo "  • Python: $(python3 --version 2>/dev/null || echo "Not installed")"
    echo "  • Environment: $([[ -f ".venv/bin/activate" ]] && echo "Virtual env available" || echo "No virtual env")"
}

# Main function
main() {
    check_version_and_enforce_upgrade "$1"
    
    case "${1:-help}" in
        # Deployment & System commands
        setup)
            setup_environment
            ;;
        upgrade)
            upgrade_script
            ;;
        start-all)
            start_all
            ;;
        deploy-all)
            echo "🚀 Deploy-All is an alias for start-all"
            start_all
            ;;
        stop-all)
            stop_all
            ;;
        system-status)
            enhanced_system_status
            ;;
        system-status-simple)
            system_status
            ;;
        deploy)
            deploy_server "$2"
            ;;
        quick-setup)
            echo "⚠️ 'quick-setup' is deprecated. Use 'setup' for full environment setup."
            quick_setup
            ;;
        
        # Telegram commands
        telegram)
            start_telegram_bot
            ;;
        telegram-deploy)
            deploy_telegram_bot
            ;;
        
        # Trading commands
        start)
            shift
            start_bot "$@"
            ;;
        # Note: stop, restart, list, status, logs commands are now handled by Telegram bot
        # These are kept for backward compatibility but should use Telegram interface
        stop)
            echo "⚠️ This command is deprecated. Use Telegram bot: /stop <symbol>"
            echo "💡 For direct access: Use Python container helper or Docker commands"
            stop_bot "$2"
            ;;
        restart)
            echo "⚠️ This command is deprecated. Use Telegram bot: /restart <symbol>"
            echo "💡 For direct access: Use Python container helper or Docker commands"
            restart_bot "$2"
            ;;
        list)
            echo "⚠️ This command is deprecated. Use Telegram bot: /list"
            echo "💡 For direct access: Use Python container helper"
            list_containers
            ;;
        status)
            echo "⚠️ This command is deprecated. Use Telegram bot: /status <symbol>"
            echo "💡 For direct access: Use Python container helper"
            get_status "$2"
            ;;
        logs)
            echo "⚠️ This command is deprecated. Use Telegram bot: /logs <symbol> [lines]"
            echo "💡 For direct access: Use Python container helper"
            get_logs "$2" "$3"
            ;;
        
        # Credential commands - Delegate to Python
        list-credentials)
            execute_python_command python3 run_telegram_app.py list-credentials
            ;;
        store-credentials)
            shift
            execute_python_command python3 run_telegram_app.py store-credentials "$@"
            ;;
        load-credentials)
            load_credentials_profile "$2"
            ;;
        show-credentials)
            execute_python_command python3 run_telegram_app.py show-credentials "$2"
            ;;

        # Bot management commands
        stop-all)
            stop_all_containers
            ;;
        list-configs)
            list_config_files
            ;;
        show-config)
            show_config_file "$2"
            ;;

        # Utility commands
        version)
            show_version
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo "❌ Unknown command: $1"
            echo ""
            echo "🔍 Did you mean:"
            echo "  • $0 setup          - Initial setup"
            echo "  • $0 start-all      - Start everything"
            echo "  • $0 system-status  - Check status"
            echo "  • $0 help           - Full help"
            exit 1
            ;;
    esac
}

# Execute main function
main "$@" 