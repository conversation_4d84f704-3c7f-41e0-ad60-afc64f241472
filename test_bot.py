#!/usr/bin/env python3
"""
Test script để test Telegram bot
"""
import asyncio
import sys
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

# Bot configuration
BOT_TOKEN = "**********************************************"
CHAT_ID = 1631630468

async def test_bot():
    """Test bot bằng cách gửi message và kiểm tra response"""
    bot = Bot(token=BOT_TOKEN)
    
    try:
        # Test 1: Kiểm tra bot info
        print("🔍 Testing bot info...")
        me = await bot.get_me()
        print(f"   Bot username: @{me.username}")
        print(f"   Bot name: {me.first_name}")
        print(f"   Bot ID: {me.id}")
        
        # Test 2: Kiểm tra chat info
        print(f"\n🔍 Testing chat info for {CHAT_ID}...")
        try:
            chat = await bot.get_chat(chat_id=CHAT_ID)
            print(f"   Chat type: {chat.type}")
            print(f"   Chat title: {chat.title or 'N/A'}")
            print(f"   Chat username: {chat.username or 'N/A'}")
        except TelegramError as e:
            print(f"   Error getting chat info: {e}")
        
        # Test 3: Gửi test message
        print(f"\n📤 Sending test message to {CHAT_ID}...")
        message = await bot.send_message(
            chat_id=CHAT_ID,
            text="🤖 Test message from bot!\n\nPlease reply with /test to test the handlers."
        )
        print(f"   Message sent successfully! Message ID: {message.message_id}")
        
        # Test 4: Kiểm tra updates
        print(f"\n📥 Checking for recent updates...")
        updates = await bot.get_updates(limit=5)
        print(f"   Found {len(updates)} recent updates")
        
        for i, update in enumerate(updates):
            print(f"   Update {i+1}: ID {update.update_id}")
            if update.message:
                print(f"     Message: {update.message.text}")
                print(f"     From: {update.effective_user.first_name}")
                print(f"     Chat: {update.effective_chat.id}")
        
        print(f"\n✅ Bot test completed successfully!")
        print(f"📋 Next steps:")
        print(f"   1. Check bot logs: docker logs telegram-bot --tail 20")
        print(f"   2. Send /test command via Telegram app")
        print(f"   3. Monitor logs for debug messages")
        
    except TelegramError as e:
        print(f"❌ Telegram error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Telegram Bot Test")
    print("=" * 40)
    
    try:
        result = asyncio.run(test_bot())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
