#!/usr/bin/env python3
"""
Wrapper script to run Central Telegram Manager with proper Python path
"""

import sys
import os
import asyncio

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Add src directory to Python path
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

# Now import and run the Telegram App
from run_telegram_app import start_telegram_bot
import os

if __name__ == "__main__":
    # Get credentials from environment
    token = os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************')
    chat_id = os.getenv('TELEGRAM_CHAT_ID', '1631630468')

    print(f"🤖 Starting Telegram Bot...")
    print(f"   Token: {token[:10]}...")
    print(f"   Chat ID: {chat_id}")

    start_telegram_bot(token, chat_id)