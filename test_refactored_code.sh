#!/bin/bash
# Test script to verify refactored code functionality
# Tests all major components after refactoring

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
print_test_header() {
    echo -e "\n${BLUE}🧪 Testing: $1${NC}"
    echo "================================"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((TESTS_PASSED++))
}

print_failure() {
    echo -e "${RED}❌ $1${NC}"
    ((TESTS_FAILED++))
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

# Test functions
test_constants_loading() {
    print_test_header "Constants Loading"
    
    # Test Python constants
    if python3 -c "from src.core.constants import CREDENTIALS_DIR, CONFIG_DIR; print('Python constants loaded')" 2>/dev/null; then
        print_success "Python constants loaded successfully"
    else
        print_failure "Failed to load Python constants"
    fi
    
    # Test shell constants
    if source src/core/shell_constants.sh && [[ -n "$CREDENTIALS_DIR" ]]; then
        print_success "Shell constants loaded successfully"
    else
        print_failure "Failed to load shell constants"
    fi
}

test_credential_utilities() {
    print_test_header "Credential Utilities"
    
    # Test credential listing
    if python3 src/cli/credentials_cli.py list >/dev/null 2>&1; then
        print_success "Credential listing works"
    else
        print_failure "Credential listing failed"
    fi
    
    # Test credential loading
    if python3 src/cli/credentials_cli.py load main >/dev/null 2>&1; then
        print_success "Credential loading works"
    else
        print_failure "Credential loading failed"
    fi
    
    # Test centralized credential utilities
    if python3 -c "from src.core.credential_utils import list_profiles; print(f'Found {len(list_profiles())} profiles')" 2>/dev/null; then
        print_success "Centralized credential utilities work"
    else
        print_failure "Centralized credential utilities failed"
    fi
}

test_telegram_app() {
    print_test_header "Telegram App"
    
    # Test telegram app credential listing
    if python3 run_telegram_app.py list-credentials >/dev/null 2>&1; then
        print_success "Telegram app credential listing works"
    else
        print_failure "Telegram app credential listing failed"
    fi
    
    # Test telegram app credential loading
    if python3 run_telegram_app.py load-credentials --profile main >/dev/null 2>&1; then
        print_success "Telegram app credential loading works"
    else
        print_failure "Telegram app credential loading failed"
    fi
}

test_bot_script() {
    print_test_header "Bot Script"
    
    # Test bot script help
    if ./bot.sh --help >/dev/null 2>&1; then
        print_success "Bot script help works"
    else
        print_failure "Bot script help failed"
    fi
    
    # Test bot script version
    if ./bot.sh version >/dev/null 2>&1; then
        print_success "Bot script version works"
    else
        print_failure "Bot script version failed"
    fi
    
    # Test credential resolution (should work until Docker step)
    if ./bot.sh start btc --profile main --amount 50 --test 2>&1 | grep -q "✅ Loaded credentials"; then
        print_success "Bot script credential resolution works"
    else
        print_failure "Bot script credential resolution failed"
    fi
}

test_directory_structure() {
    print_test_header "Directory Structure"
    
    # Test that constants create correct directories
    if python3 -c "from src.core.constants import ensure_directories; ensure_directories()" 2>/dev/null; then
        print_success "Directory creation works"
    else
        print_failure "Directory creation failed"
    fi
    
    # Check if directories exist
    if [[ -d "$HOME/.autotrader/credentials" ]]; then
        print_success "Credentials directory exists"
    else
        print_failure "Credentials directory missing"
    fi
    
    if [[ -d "configs" && -d "data" && -d "logs" ]]; then
        print_success "Project directories exist"
    else
        print_failure "Project directories missing"
    fi
}

test_no_code_duplication() {
    print_test_header "Code Duplication Check"
    
    # Check for hardcoded .autotrader paths
    if grep -r "\.autotrader" --include="*.py" --include="*.sh" . | grep -v "src/core/" | grep -v "test_" | grep -v ".git" >/dev/null 2>&1; then
        print_warning "Found hardcoded .autotrader paths outside core modules"
        grep -r "\.autotrader" --include="*.py" --include="*.sh" . | grep -v "src/core/" | grep -v "test_" | grep -v ".git" | head -5
    else
        print_success "No hardcoded .autotrader paths found"
    fi
    
    # Check for hardcoded credential paths
    if grep -r "credentials/" --include="*.py" --include="*.sh" . | grep -v "src/core/" | grep -v "test_" | grep -v ".git" >/dev/null 2>&1; then
        print_warning "Found hardcoded credential paths outside core modules"
    else
        print_success "No hardcoded credential paths found"
    fi
    
    # Check for duplicate function definitions
    if grep -r "def normalize_symbol\|normalize_symbol()" --include="*.py" --include="*.sh" . | wc -l | grep -q "^1$"; then
        print_success "No duplicate normalize_symbol functions"
    else
        print_warning "Found duplicate normalize_symbol functions"
    fi
}

test_imports() {
    print_test_header "Import Structure"
    
    # Test that all imports work
    if python3 -c "
from src.core.constants import *
from src.core.credential_utils import *
print('All imports successful')
" 2>/dev/null; then
        print_success "All Python imports work"
    else
        print_failure "Python imports failed"
    fi
    
    # Test shell sourcing
    if bash -c "source src/core/shell_constants.sh && echo 'Shell constants loaded'" >/dev/null 2>&1; then
        print_success "Shell constants can be sourced"
    else
        print_failure "Shell constants sourcing failed"
    fi
}

# Main test execution
main() {
    echo -e "${BLUE}🚀 AutoTrader Refactored Code Test Suite${NC}"
    echo "========================================"
    echo "Testing all components after refactoring..."
    
    test_constants_loading
    test_credential_utilities
    test_telegram_app
    test_bot_script
    test_directory_structure
    test_no_code_duplication
    test_imports
    
    # Summary
    echo -e "\n${BLUE}📊 Test Summary${NC}"
    echo "==============="
    echo -e "${GREEN}✅ Passed: $TESTS_PASSED${NC}"
    echo -e "${RED}❌ Failed: $TESTS_FAILED${NC}"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 All tests passed! Refactoring successful.${NC}"
        return 0
    else
        echo -e "\n${RED}💥 Some tests failed. Please review the issues above.${NC}"
        return 1
    fi
}

# Run tests
main "$@"
