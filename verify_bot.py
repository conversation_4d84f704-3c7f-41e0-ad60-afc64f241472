#!/usr/bin/env python3
"""
Script để verify bot đang hoạt động và gửi test message
"""
import asyncio
import sys
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

# Bot configuration
BOT_TOKEN = "**********************************************"
CHAT_ID = 1631630468

async def verify_bot():
    """Verify bot và gửi test message"""
    bot = Bot(token=BOT_TOKEN)
    
    try:
        print("🤖 Verifying bot status...")
        me = await bot.get_me()
        print(f"   ✅ Bot @{me.username} is active")
        
        print(f"\n📤 Sending test message with instructions...")
        message = await bot.send_message(
            chat_id=CHAT_ID,
            text="""🤖 **Bot Test Message**

Bot đang hoạt động! Để test các handlers:

🔹 **Gửi /start** - Test start command
🔹 **Gửi /test** - Test test command  
🔹 **Gử<PERSON> /help** - Test help command
🔹 **<PERSON><PERSON><PERSON> b<PERSON><PERSON> kỳ text nào** - Test message handler

📊 **Monitor logs:**
```
docker logs telegram-bot --tail 20 -f
```

Sau khi gửi commands, check logs để xem debug messages!""",
            parse_mode='Markdown'
        )
        
        print(f"   ✅ Test message sent! Message ID: {message.message_id}")
        print(f"\n📋 Next steps:")
        print(f"   1. Open Telegram app")
        print(f"   2. Find bot @{me.username}")
        print(f"   3. Send /start command")
        print(f"   4. Monitor logs: docker logs telegram-bot --tail 20 -f")
        print(f"   5. Look for debug messages starting with '🔄 POLLING DEBUG'")
        
        return True
        
    except TelegramError as e:
        print(f"❌ Telegram error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Bot Verification")
    print("=" * 30)
    
    try:
        result = asyncio.run(verify_bot())
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
