#!/usr/bin/env python3
"""
Test script để test Telegram bot handlers trực tiếp
"""
import asyncio
import sys
import os
sys.path.append('src')

from telegram import Update, Message, User, Chat
from telegram.ext import ContextTypes
from infrastructure.telegram.handlers.main_handler import MainHandler

# Mock data
MOCK_USER = User(
    id=1631630468,
    is_bot=False,
    first_name="Test User",
    username="hoangtrungdev"
)

MOCK_CHAT = Chat(
    id=1631630468,
    type="private"
)

async def create_mock_update(text: str, message_id: int = 1) -> Update:
    """Tạo mock Update object"""
    message = Message(
        message_id=message_id,
        date=None,
        chat=MOCK_CHAT,
        from_user=MOCK_USER,
        text=text
    )
    
    return Update(
        update_id=message_id,
        message=message
    )

async def create_mock_context():
    """Tạo mock Context object"""
    class MockContext:
        def __init__(self):
            self.args = []
            self.user_data = {}
            self.chat_data = {}
            self.bot_data = {}
    
    return MockContext()

async def test_handlers():
    """Test các handlers trực tiếp"""
    print("🚀 Starting Handler Test")
    print("=" * 40)
    
    try:
        # Initialize handler
        print("🔧 Initializing MainHandler...")
        handler = MainHandler()
        
        # Test /start command
        print("\n📤 Testing /start command...")
        update = await create_mock_update("/start")
        context = await create_mock_context()
        
        try:
            await handler.handle_start(update, context)
            print("   ✅ /start handler executed successfully")
        except Exception as e:
            print(f"   ❌ /start handler error: {e}")
        
        # Test /test command
        print("\n📤 Testing /test command...")
        update = await create_mock_update("/test")
        context = await create_mock_context()
        
        try:
            await handler.handle_test(update, context)
            print("   ✅ /test handler executed successfully")
        except Exception as e:
            print(f"   ❌ /test handler error: {e}")
        
        # Test /help command
        print("\n📤 Testing /help command...")
        update = await create_mock_update("/help")
        context = await create_mock_context()
        
        try:
            await handler.handle_help(update, context)
            print("   ✅ /help handler executed successfully")
        except Exception as e:
            print(f"   ❌ /help handler error: {e}")
        
        # Test debug handler
        print("\n📤 Testing debug handler...")
        update = await create_mock_update("Hello bot!")
        context = await create_mock_context()
        
        try:
            await handler.debug_all_updates(update, context)
            print("   ✅ Debug handler executed successfully")
        except Exception as e:
            print(f"   ❌ Debug handler error: {e}")
        
        print(f"\n✅ Handler test completed!")
        print(f"📋 All handlers are working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Fatal error during handler test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(test_handlers())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
