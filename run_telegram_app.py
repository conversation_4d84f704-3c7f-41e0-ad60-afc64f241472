#!/usr/bin/env python3
"""
AutoTrader Telegram Application
Enhanced to handle all commands and logic
"""

import sys
import os
import argparse
import asyncio
import logging
import json
from pathlib import Path

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient
from src.infrastructure.telegram.handlers.improved_command_handler import ImprovedTelegramCommandHandler

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def list_credentials():
    """List stored credentials from unified directory"""
    # Use unified credentials directory (.autotrader)
    creds_dir = Path.home() / ".autotrader" / "credentials"

    if not creds_dir.exists():
        print("❌ No credentials directory found")
        print("💡 Use '/addcreds' in Telegram bot to add credentials")
        return 1

    # Find all credential files (JSON and .env)
    json_files = list(creds_dir.glob("*.json"))
    env_files = list(creds_dir.glob("*.env"))

    if not json_files and not env_files:
        print("📭 No credential profiles found")
        print("💡 Use '/addcreds' in Telegram bot to add credentials")
        return 0

    print("📋 Listing Credentials")
    print("======================")
    print("")
    print("✅ Found credential profiles:")
    print("")

    # Process JSON files first
    for json_file in json_files:
        try:
            cred_data = json.loads(json_file.read_text())
            profile = cred_data.get('profile', json_file.stem)
            display_name = cred_data.get('display_name', profile)
            print(f"🔑 {profile}")
            print(f"   Display Name: {display_name}")
            print("")
        except Exception as e:
            print(f"⚠️ Error reading {json_file.name}: {e}")

    # Process .env files for backward compatibility
    for env_file in env_files:
        profile = env_file.stem
        display_file = creds_dir / f"{profile}.display"

        display_name = profile
        if display_file.exists():
            try:
                display_name = display_file.read_text().strip()
            except:
                display_name = profile
        
        print(f"  • {profile} ({display_name})")
    
    return 0

def store_credentials(profile, api_key, api_secret, display_name=None):
    """Store API credentials securely"""
    if not profile or not api_key or not api_secret:
        print("❌ Usage: store-credentials <profile> <api_key> <api_secret> [display_name]")
        return 1
    
    if display_name is None:
        display_name = profile
    
    # Use unified credentials directory (.autotrader)
    creds_dir = Path.home() / ".autotrader" / "credentials"
    creds_dir.mkdir(parents=True, exist_ok=True)

    print(f"🔑 Storing credentials for profile: {profile}")

    # Store credentials in JSON format (unified with Python CLI)
    json_file = creds_dir / f"{profile}.json"
    cred_data = {
        "profile": profile,
        "api_key": api_key,
        "api_secret": api_secret,
        "display_name": display_name,
        "created": "telegram_bot",
        "version": "1.0"
    }

    json_file.write_text(json.dumps(cred_data, indent=2))
    json_file.chmod(0o600)  # Secure permissions

    print("✅ Credentials stored successfully")
    print(f"   Profile: {profile}")
    print(f"   Display Name: {display_name}")
    print(f"💡 Load with /loadcreds command")

    return 0

def load_credentials(profile="default"):
    """Load credentials into environment from unified directory"""
    # Use unified credentials directory (.autotrader)
    creds_dir = Path.home() / ".autotrader" / "credentials"

    # Try JSON format first
    json_file = creds_dir / f"{profile}.json"
    if json_file.exists():
        try:
            cred_data = json.loads(json_file.read_text())
            os.environ['BYBIT_API_KEY'] = cred_data['api_key']
            os.environ['BYBIT_API_SECRET'] = cred_data['api_secret']
            print(f"✅ Loaded credentials for profile: {profile}")
            return 0
        except Exception as e:
            print(f"❌ Failed to load JSON credentials: {e}")

    # Try legacy .env format for backward compatibility
    env_file = creds_dir / f"{profile}.env"
    if env_file.exists():
        print(f"🔄 Loading credentials for profile: {profile}")

        # Parse .env file
        credentials = {}
        try:
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and '=' in line:
                        key, value = line.split('=', 1)
                        credentials[key] = value
                        os.environ[key] = value
            print(f"✅ Loaded credentials for profile: {profile}")
            return 0
        except Exception as e:
            print(f"❌ Failed to load credentials: {e}")
            return 1

    print(f"❌ Profile not found: {profile}")
    print("💡 Use '/listcreds' in Telegram bot to list profiles")
    return 1
    
    # Get display name
    display_file = creds_dir / f"{profile}.display"
    display_name = profile
    if display_file.exists():
        try:
            display_name = display_file.read_text().strip()
        except:
            display_name = profile
    
    api_key = credentials.get('BYBIT_API_KEY', '')
    
    print("✅ Credentials loaded")
    print(f"   Profile: {profile}")
    print(f"   Display Name: {display_name}")
    print(f"   API Key: {api_key[:5]}...")
    
    return 0

def show_credentials(profile="default"):
    """Show credentials details (masked)"""
    creds_dir = Path.home() / ".traderbot" / "credentials"
    creds_file = creds_dir / f"{profile}.env"

    if not creds_file.exists():
        print(f"❌ Profile not found: {profile}")
        print("💡 Use '/listcreds' in Telegram bot to list profiles")
        return 1

    print(f"🔐 Credential Details for Profile: {profile}")
    print("=" * 50)

    # Get display name
    display_file = creds_dir / f"{profile}.display"
    display_name = profile
    if display_file.exists():
        try:
            display_name = display_file.read_text().strip()
        except:
            display_name = profile

    # Parse .env file
    credentials = {}
    try:
        with open(creds_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line:
                    key, value = line.split('=', 1)
                    credentials[key] = value
    except Exception as e:
        print(f"❌ Failed to read credentials: {e}")
        return 1

    api_key = credentials.get('BYBIT_API_KEY', '')

    print(f"📝 Profile Name: {profile}")
    print(f"🏷️  Display Name: {display_name}")
    print(f"🔑 API Key: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else '***'}")
    print(f"🔐 API Secret: {'*' * 20}")

    # File info
    import os
    import time
    stat = os.stat(creds_file)
    created_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_ctime))
    modified_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime))

    print(f"📅 Created: {created_time}")
    print(f"🔄 Modified: {modified_time}")
    print(f"🔒 File Permissions: {oct(stat.st_mode)[-3:]}")

    return 0

def check_credentials():
    """Check if credentials are available"""
    creds_dir = Path.home() / ".traderbot" / "credentials"
    
    if not creds_dir.exists():
        return False
    
    # Check if any .env files exist
    env_files = list(creds_dir.glob("*.env"))
    return len(env_files) > 0

async def send_message(token, chat_id, message=None, command=None, returncode=0, stdout="", stderr=""):
    """Send message via Telegram"""
    try:
        client = TelegramAPIClient(token)
        
        if command:
            # Format command response
            if returncode == 0:
                if stdout:
                    formatted_message = f"✅ **Command Success**\n\n**Command:** `{command}`\n\n**Output:**\n```\n{stdout}\n```"
                else:
                    formatted_message = f"✅ **Command Success**\n\n**Command:** `{command}`\n\n✅ Command completed successfully"
            else:
                error_text = stderr if stderr else stdout if stdout else f"Command failed with exit code {returncode}"
                formatted_message = f"❌ **Command Error**\n\n**Command:** `{command}`\n\n**Error:**\n```\n{error_text}\n```"
        else:
            formatted_message = message
        
        await client.send_message(chat_id, formatted_message)
        print("✅ Message sent successfully")
        
    except Exception as e:
        print(f"❌ Failed to send message: {e}")
        return 1
    
    return 0

def start_telegram_bot(token, chat_id):
    """Start the Telegram bot"""
    try:
        # Setup logging
        setup_logging()

        # Check if credentials exist before starting
        if not check_credentials():
            print("⚠️ No credentials found. Users will need to add credentials first.")

        handler = ImprovedTelegramCommandHandler(token, int(chat_id))

        print("🤖 Starting Telegram bot...")
        print(f"   Bot Token: {token[:10]}...")
        print(f"   Chat ID: {chat_id}")

        # Start the bot - this will run until interrupted
        handler.start()

    except KeyboardInterrupt:
        print("\n🛑 Telegram bot stopped by user")
    except Exception as e:
        print(f"❌ Failed to start Telegram bot: {e}")
        logging.error(f"Failed to start Telegram bot: {e}")
        return 1

    return 0

def validate_createbot_prerequisites():
    """Validate prerequisites for creating a bot"""
    errors = []
    warnings = []
    
    # Check Docker
    import subprocess
    try:
        subprocess.run(['docker', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        errors.append("Docker is not installed or not accessible")
    
    # Check credentials
    if not check_credentials():
        errors.append("No API credentials found. Add credentials first with /addcreds")
    
    # Check Python environment
    if not sys.executable:
        warnings.append("Python environment may not be properly configured")
    
    return errors, warnings

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Telegram Bot for AutoTrader')
    parser.add_argument('action', choices=['start', 'list-credentials', 'store-credentials', 'load-credentials', 'show-credentials'], help='Action to perform')
    parser.add_argument('--token', help='Telegram bot token (required for start action)')
    parser.add_argument('--chat-id', help='Chat ID for notifications (required for start action)')
    parser.add_argument('profile', nargs='?', help='Profile name for credentials operations')
    parser.add_argument('api_key', nargs='?', help='API key for store-credentials')
    parser.add_argument('api_secret', nargs='?', help='API secret for store-credentials')
    parser.add_argument('display_name', nargs='?', help='Display name for store-credentials')

    args = parser.parse_args()

    if args.action == 'start':
        if not args.token or not args.chat_id:
            print("❌ --token and --chat-id are required for start action")
            return 1
        return start_telegram_bot(args.token, args.chat_id)

    elif args.action == 'list-credentials':
        return list_credentials()

    elif args.action == 'store-credentials':
        if not args.profile or not args.api_key or not args.api_secret:
            print("❌ Usage: store-credentials <profile> <api_key> <api_secret> [display_name]")
            return 1
        return store_credentials(args.profile, args.api_key, args.api_secret, args.display_name)

    elif args.action == 'load-credentials':
        profile = args.profile if args.profile else "default"
        return load_credentials(profile)

    elif args.action == 'show-credentials':
        profile = args.profile if args.profile else "default"
        return show_credentials(profile)

    return 0

if __name__ == "__main__":
    sys.exit(main()) 