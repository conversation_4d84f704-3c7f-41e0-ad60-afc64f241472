#!/usr/bin/env python3
"""
AutoTrader Telegram Application
Enhanced to handle all commands and logic
"""

import sys
import os
import argparse
import asyncio
import logging
from pathlib import Path

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import core utilities
from src.core.constants import (
    TELEGRAM_BOT_TOKEN_VAR,
    TELEGRAM_CHAT_ID_VAR,
    ensure_directories
)
from src.core.credential_utils import (
    store_credentials,
    load_credentials,
    list_profiles,
    set_environment_variables
)

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient
from src.infrastructure.telegram.handlers.main_handler import MainTelegramHand<PERSON>

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def list_credentials():
    """List stored credentials using centralized utility"""
    profiles = list_profiles()

    if not profiles:
        print("📭 No credential profiles found")
        print("💡 Use '/addcreds' in Telegram bot to add credentials")
        return 0

    print("📋 Listing Credentials")
    print("======================")
    print("")
    print("✅ Found credential profiles:")
    print("")

    for profile_info in profiles:
        profile = profile_info['profile']
        display_name = profile_info['display_name']
        format_type = profile_info.get('format', 'json')

        print(f"🔑 {profile}")
        print(f"   Display Name: {display_name}")
        print(f"   Format: {format_type}")
        if profile_info.get('error'):
            print("   ⚠️ Error reading file")
        print("")
        
        print(f"  • {profile} ({display_name})")
    
    return 0

def store_credentials_cli(profile, api_key, api_secret, display_name=None):
    """Store API credentials securely"""
    if not profile or not api_key or not api_secret:
        print("❌ Usage: store-credentials <profile> <api_key> <api_secret> [display_name]")
        return 1

    if display_name is None:
        display_name = profile

    print(f"🔑 Storing credentials for profile: {profile}")

    # Use centralized credential storage
    success = store_credentials(profile, api_key, api_secret, display_name, 'telegram_bot')

    if success:
        print("✅ Credentials stored successfully")
        print(f"   Profile: {profile}")
        print(f"   Display Name: {display_name}")
        print(f"💡 Load with /loadcreds command")
        return 0
    else:
        print("❌ Failed to store credentials")
        return 1

def load_credentials_to_env(profile="default"):
    """Load credentials into environment using centralized utility"""
    print(f"🔄 Loading credentials for profile: {profile}")

    success = set_environment_variables(profile)

    if success:
        print(f"✅ Loaded credentials for profile: {profile}")
        return 0
    else:
        print(f"❌ Profile not found: {profile}")
        print("💡 Use '/listcreds' in Telegram bot to list profiles")
        return 1

def show_credentials(profile="default"):
    """Show credentials details (masked) using centralized utility"""
    cred_data = load_credentials(profile)

    if not cred_data:
        print(f"❌ Profile not found: {profile}")
        print("💡 Use '/listcreds' in Telegram bot to list profiles")
        return 1

    print(f"🔐 Credential Details for Profile: {profile}")
    print("=" * 50)
    print(f"   Display Name: {cred_data['display_name']}")
    print(f"   API Key: {cred_data['api_key'][:8]}...")
    print(f"   API Secret: {cred_data['api_secret'][:8]}...")

    return 0
def check_credentials():
    """Check if credentials are available using centralized utility"""
    profiles = list_profiles()
    return len(profiles) > 0

async def send_message(token, chat_id, message=None, command=None, returncode=0, stdout="", stderr=""):
    """Send message via Telegram"""
    try:
        client = TelegramAPIClient(token)
        
        if command:
            # Format command response
            if returncode == 0:
                if stdout:
                    formatted_message = f"✅ **Command Success**\n\n**Command:** `{command}`\n\n**Output:**\n```\n{stdout}\n```"
                else:
                    formatted_message = f"✅ **Command Success**\n\n**Command:** `{command}`\n\n✅ Command completed successfully"
            else:
                error_text = stderr if stderr else stdout if stdout else f"Command failed with exit code {returncode}"
                formatted_message = f"❌ **Command Error**\n\n**Command:** `{command}`\n\n**Error:**\n```\n{error_text}\n```"
        else:
            formatted_message = message
        
        await client.send_message(chat_id, formatted_message)
        print("✅ Message sent successfully")
        
    except Exception as e:
        print(f"❌ Failed to send message: {e}")
        return 1
    
    return 0

def start_telegram_bot(token, chat_id):
    """Start the Telegram bot"""
    try:
        # Setup logging
        setup_logging()

        # Check if credentials exist before starting
        if not check_credentials():
            print("⚠️ No credentials found. Users will need to add credentials first.")

        handler = MainTelegramHandler(token, int(chat_id))

        print("🤖 Starting Telegram bot...")
        print(f"   Bot Token: {token[:10]}...")
        print(f"   Chat ID: {chat_id}")

        # Start the bot - this will run until interrupted
        handler.start()

    except KeyboardInterrupt:
        print("\n🛑 Telegram bot stopped by user")
    except Exception as e:
        print(f"❌ Failed to start Telegram bot: {e}")
        logging.error(f"Failed to start Telegram bot: {e}")
        return 1

    return 0

def validate_createbot_prerequisites():
    """Validate prerequisites for creating a bot"""
    errors = []
    warnings = []
    
    # Check Docker
    import subprocess
    try:
        subprocess.run(['docker', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        errors.append("Docker is not installed or not accessible")
    
    # Check credentials
    if not check_credentials():
        errors.append("No API credentials found. Add credentials first with /addcreds")
    
    # Check Python environment
    if not sys.executable:
        warnings.append("Python environment may not be properly configured")
    
    return errors, warnings

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Telegram Bot for AutoTrader')
    parser.add_argument('action', choices=['start', 'list-credentials', 'store-credentials', 'load-credentials', 'show-credentials'], help='Action to perform')
    parser.add_argument('--token', help='Telegram bot token (required for start action)')
    parser.add_argument('--chat-id', help='Chat ID for notifications (required for start action)')
    parser.add_argument('profile', nargs='?', help='Profile name for credentials operations')
    parser.add_argument('api_key', nargs='?', help='API key for store-credentials')
    parser.add_argument('api_secret', nargs='?', help='API secret for store-credentials')
    parser.add_argument('display_name', nargs='?', help='Display name for store-credentials')

    args = parser.parse_args()

    if args.action == 'start':
        if not args.token or not args.chat_id:
            print("❌ --token and --chat-id are required for start action")
            return 1
        return start_telegram_bot(args.token, args.chat_id)

    elif args.action == 'list-credentials':
        return list_credentials()

    elif args.action == 'store-credentials':
        if not args.profile or not args.api_key or not args.api_secret:
            print("❌ Usage: store-credentials <profile> <api_key> <api_secret> [display_name]")
            return 1
        return store_credentials_cli(args.profile, args.api_key, args.api_secret, args.display_name)

    elif args.action == 'load-credentials':
        profile = args.profile if args.profile else "default"
        return load_credentials_to_env(profile)

    elif args.action == 'show-credentials':
        profile = args.profile if args.profile else "default"
        return show_credentials(profile)

    return 0

if __name__ == "__main__":
    sys.exit(main()) 