#!/bin/bash
# Migration script to move credentials from .traderbot to .autotrader
# This ensures unified credential management

set -euo pipefail

echo "🔄 AutoTrader Credentials Migration"
echo "==================================="
echo ""

OLD_CREDS_DIR="$HOME/.traderbot/credentials"
NEW_CREDS_DIR="$HOME/.autotrader/credentials"

# Check if old directory exists
if [[ ! -d "$OLD_CREDS_DIR" ]]; then
    echo "✅ No old credentials directory found at $OLD_CREDS_DIR"
    echo "   Nothing to migrate."
    exit 0
fi

# Create new directory if it doesn't exist
mkdir -p "$NEW_CREDS_DIR"

# Check if there are any files to migrate
OLD_FILES=($(find "$OLD_CREDS_DIR" -name "*.env" -o -name "*.display" 2>/dev/null || true))

if [[ ${#OLD_FILES[@]} -eq 0 ]]; then
    echo "✅ No credential files found in old directory"
    echo "   Nothing to migrate."
    exit 0
fi

echo "📁 Found old credentials directory: $OLD_CREDS_DIR"
echo "📁 Target directory: $NEW_CREDS_DIR"
echo ""

# List files to be migrated
echo "📋 Files to migrate:"
for file in "${OLD_FILES[@]}"; do
    echo "   • $(basename "$file")"
done
echo ""

# Ask for confirmation
echo "❓ Do you want to migrate these credentials? (y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo "❌ Migration cancelled"
    exit 0
fi

echo ""
echo "🔄 Starting migration..."

MIGRATED_COUNT=0
ERROR_COUNT=0

# Migrate .env files and convert to JSON format
for env_file in "$OLD_CREDS_DIR"/*.env; do
    if [[ -f "$env_file" ]]; then
        profile=$(basename "$env_file" .env)
        
        echo "🔄 Migrating profile: $profile"
        
        # Read .env file
        api_key=""
        api_secret=""
        
        while IFS='=' read -r key value; do
            case "$key" in
                "BYBIT_API_KEY")
                    api_key="$value"
                    ;;
                "BYBIT_API_SECRET")
                    api_secret="$value"
                    ;;
            esac
        done < "$env_file"
        
        # Get display name
        display_file="$OLD_CREDS_DIR/${profile}.display"
        display_name="$profile"
        if [[ -f "$display_file" ]]; then
            display_name=$(cat "$display_file" 2>/dev/null || echo "$profile")
        fi
        
        # Create JSON format
        json_file="$NEW_CREDS_DIR/${profile}.json"
        
        if [[ -n "$api_key" && -n "$api_secret" ]]; then
            cat > "$json_file" << EOF
{
  "profile": "$profile",
  "api_key": "$api_key",
  "api_secret": "$api_secret",
  "display_name": "$display_name",
  "created": "migrated_from_traderbot",
  "version": "1.0"
}
EOF
            chmod 600 "$json_file"
            echo "   ✅ Migrated to: $json_file"
            ((MIGRATED_COUNT++))
        else
            echo "   ❌ Invalid credentials in $env_file"
            ((ERROR_COUNT++))
        fi
    fi
done

echo ""
echo "📊 Migration Summary:"
echo "   ✅ Successfully migrated: $MIGRATED_COUNT profiles"
if [[ $ERROR_COUNT -gt 0 ]]; then
    echo "   ❌ Errors: $ERROR_COUNT profiles"
fi

if [[ $MIGRATED_COUNT -gt 0 ]]; then
    echo ""
    echo "🎉 Migration completed successfully!"
    echo ""
    echo "💡 Next steps:"
    echo "   1. Test the migrated credentials:"
    echo "      python3 src/cli/credentials_cli.py list"
    echo ""
    echo "   2. If everything works correctly, you can remove the old directory:"
    echo "      rm -rf $OLD_CREDS_DIR"
    echo ""
    echo "   3. Restart your Telegram bot to use the new credentials location"
else
    echo ""
    echo "⚠️ No credentials were migrated"
fi
